{"appTitle": "Smart Yambol", "guest": "Guest", "settings": "Settings", "darkMode": "Dark Mode", "language": "Language", "terms": "Terms & Conditions", "login": "<PERSON><PERSON>", "tourism": "Tourism", "tourismDescription": "Explore the beauty of Yambol and create lifelong memories!", "weather": "Weather", "weatherDescription": "Stay prepared! See the forecast and air quality.", "events": "Events", "eventsDescription": "Check out cultural and sports events in the city.", "repairs": "Repairs", "repairsDescription": "Stay informed! See where and when repairs are happening.", "parking": "Parking", "parkingDescription": "Find a parking spot or pay for blue zone parking via SMS.", "generalInfo": "General Info", "generalInfoDescription": "Discover everything you need to know about Yambol in one place.", "transport": "Transport", "transportDescription": "Up-to-date information on public transport routes and schedules.", "cameras": "Cameras", "camerasDescription": "Live-stream views from key locations in the city.", "news": "News", "newsDescription": "Stay updated! Follow the latest news and events.", "taxes": "Taxes", "taxesDescription": "View and pay your municipal taxes quickly and easily.", "publicServices": "Services", "publicServicesDescription": "Take advantage of the services we offer!", "reports": "Reports", "reportsDescription": "Report an issue in the city and help make it better.", "favorites": "Favorites", "location": "Location", "home": "Home", "messages": "Messages", "emailOrUsername": "Email or Username", "password": "Password", "forgotPassword": "Forgot Password?", "loginButton": "Log In", "orContinueWith": "- or continue with -", "createNewAccount": "Create a new account", "registerHere": "here", "register": "Register", "confirmPassword": "Confirm Password", "agreeToTerms": "By clicking the Register button, you agree to the", "registerTerms": "terms & conditions", "registerButton": "Register", "alreadyHaveAccount": "Already have an account?", "loginHere": "Log in here", "dontHaveAccount": "Don't have an account?", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "emailRequired": "Email is required", "invalidEmail": "Please enter a valid email", "registrationSuccessful": "Registration successful!", "passwordRequired": "Password is required", "passwordTooShort": "Password is too short", "confirmPasswordRequired": "Confirm password required", "passwordsDoNotMatch": "Passwords do not match", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "tourismWelcomeMessage": "Explore Yambol! Discover its landmarks and get inspired by its history.", "tourismLegendsAndMyths": "Legends and Myths", "tourismSights": "Landmarks", "tourismCulturalAndArtisticSites": "Cultural and Artistic Sites", "tourismRoutesAndActivities": "Routes and Activities", "tourismFamilyEntertainment": "Family Entertainment", "tourismNightlife": "Nightlife", "tourismTransport": "Transport", "tourismTravelAgencies": "Travel Agencies", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed", "legendsAndMythsWelcomeMessage": "Discover the mysteries of Yambol: Legends and myths that will captivate you", "weatherTitle": "Weather", "cityName": "Yambol", "wind": "Wind", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "errorLoadingWeather": "Error loading weather data", "retry": "Retry", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "eventsWelcomeMessage": "Don't miss anything! Explore cultural and sports events in the city!", "search": "Search...", "tourismCulturalSites": "Cultural sites", "map": "Map", "all": "All", "sportEvents": "Sport", "cultureEvents": "Culture", "celebrationEvents": "Celebrations", "mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat", "sun": "Sun", "january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December", "emailConfirmationTitle": "Email Confirmation", "emailConfirmationMessage": "Please check your email to confirm your account.", "didNotReceiveEmail": "Didn't receive the email?", "resendVerificationEmail": "Resend Verification Email", "verificationEmailResent": "Verification email resent!", "errorOccurred": "Error occurred", "emailVerificationTitle": "Email Verification", "verifyEmailButton": "<PERSON><PERSON><PERSON>", "emailVerifiedSuccessfully": "Email verified successfully!", "generalInfoWelcomeMessage": "Yambol – business, education, and essential city information!", "gasStations": "Gas stations", "generalInformationFull": "General information", "shop": "Shops", "restaurants": "Restaurants", "coffee": "Coffee", "bars": "Bars", "pastryShops": "Pastry shops", "establishments": "Establishments", "establishmentsWelcomeMessage": "Cozy restaurants, traditional cuisine, and modern venues with a unique atmosphere!", "hotels": "Hotels", "guestHouses": "Guest houses", "accommodation": "Accommodation", "accommodationWelcomeMessage": "Comfortable hotels, cozy guesthouses, and great places to stay for every taste!", "finance": "Finance", "banks": "Banks", "currencyExchanges": "Currency Exchange", "insuranceCompanies": "Insurance", "atms": "ATMs", "financeYambolWelcomeMessage": "Secure banking services, affordable loans, and financial solutions for you in Yambol!", "bioShops": "Bio shops", "farms": "Farms", "recycling": "Recycling", "ecoInitiatives": "Eco initiatives", "ecology": "Ecology", "ecoYambolWelcomeMessage": "Clean nature, sustainable future, and eco-friendly solutions for Yambol!", "culture": "Culture", "museums": "Museums", "theaters": "Theaters", "galleries": "Galleries", "cultureYambolWelcomeMessage": "Rich cultural heritage, inspiring art, and traditions in Yambol!", "education": "Education", "kindergardens": "Kindergardens", "nursery": "Nursery", "childNutritionCenter": "Child nutrition center", "schools": "Schools", "universities": "Universities", "developmentCenters": "Development centers", "health": "Health", "pharmacies": "Pharmacies", "medicalEstablishments": "Medical establishments", "doctorsOffices": "Doctors offices", "medicalLabs": "Medical labs", "veterinaries": "Veterinaries", "healthYambolWelcomeMessage": "Health and care for everyone – quality medical services and a healthy lifestyle in Yambol!", "educationYambolWelcomeMessage": "Quality education for a bright future – innovative learning and development in Yambol!", "sport": "Sport", "sportFacilities": "Sport Facilities", "sportYambolWelcomeMessage": "Sports and an active lifestyle – modern facilities and opportunities for movement in Yambol!", "newsYambolWelcomeMessage": "Stay informed! Follow the latest news and events.", "close": "Close", "description": "Description", "municipalityTaxes": "Municipality Taxes", "information": "Information", "chooseWayToPay": "You can pay online using your CIN or with an electronic signature (which must be installed on your device).", "waysToPay": "Ways to pay", "kinNumber": "Pay with KIN", "kinDesc": "Fast and convenient payment with your KIN number", "electronicSignature": "Pay with electronic signature", "electronicSignatureDesc": "Secure payment with your electronic signature", "userHelp": "Need help? Contact us at phone: 0875 333 844", "problemElectronicSignature": "The electronic signature payment link could not be opened.", "problemKin": "The KIN payment link could not be opened.", "navigate": "Navigate", "business": "Business", "landmarks": "Landmarks", "healthcare": "Healthcare", "sports": "Sports", "applyFilters": "Apply Filters", "filterCategories": "Filter Categories"}