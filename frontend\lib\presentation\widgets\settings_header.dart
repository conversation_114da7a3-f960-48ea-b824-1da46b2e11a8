import 'package:flutter/material.dart';
import '../../l10n/generated/l10n.dart';

class SettingsHeader extends StatelessWidget {
  const SettingsHeader({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context); // ✅ Get current theme

    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // ✅ Profile Section with Background Image
        Container(
          width: double.infinity,
          height: 200,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            image: const DecorationImage(
              image: AssetImage(
                  'assets/images/settings_image.png'), // ✅ Background Image
              fit: BoxFit.cover,
            ),
            color: Colors.green.withOpacity(0.4), // ✅ Overlay Effect
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircleAvatar(
                radius: 40,
                backgroundColor: Colors.white,
                child: Icon(Icons.person, size: 50, color: Colors.black45),
              ),
              const SizedBox(height: 8),
              Text(
                S.of(context).guest, // ✅ Translated "Guest"
                style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
