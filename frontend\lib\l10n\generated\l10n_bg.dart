import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for Bulgarian (`bg`).
class SBg extends S {
  SBg([String locale = 'bg']) : super(locale);

  @override
  String get appTitle => 'Смарт Ямбол';

  @override
  String get guest => 'Гост';

  @override
  String get settings => 'Настройки';

  @override
  String get darkMode => 'Тъмна тема';

  @override
  String get language => 'Език';

  @override
  String get terms => 'Условия за използване';

  @override
  String get login => 'Вход';

  @override
  String get tourism => 'Туризъм';

  @override
  String get tourismDescription => '„Ямбол те кани – открий неговата красота и създай спомени за цял живот!“';

  @override
  String get weather => 'Времето';

  @override
  String get weatherDescription => 'Бъди подготвен! Виж прогнозата и качеството на въздуха.';

  @override
  String get events => 'Събития';

  @override
  String get eventsDescription => 'Разгледай културни и спортни събития в града.';

  @override
  String get repairs => 'Ремонти';

  @override
  String get repairsDescription => 'Остани информиран! Виж къде и кога се извършват ремонти.';

  @override
  String get parking => 'Паркинг';

  @override
  String get parkingDescription => 'Паркирай без стрес! Намери свободно място или изпрати SMS за синя зона.';

  @override
  String get generalInfo => 'Обща Инф.';

  @override
  String get generalInfoDescription => 'Открий града! Всичко, което трябва да знаеш за Ямбол, на едно място.';

  @override
  String get transport => 'Транспорт';

  @override
  String get transportDescription => 'Планирай пътуването си лесно – виж разписания и актуална информация за градския транспорт в реално време.';

  @override
  String get cameras => 'Камери';

  @override
  String get camerasDescription => 'Виж какво се случва! Гледай на живо от ключови места в града.';

  @override
  String get news => 'Новини';

  @override
  String get newsDescription => 'Бъди в час! Следи актуалните новини и събития.';

  @override
  String get taxes => 'Данъци';

  @override
  String get taxesDescription => 'Виж и плати своите общински данъци бързо и лесно.';

  @override
  String get publicServices => 'Общ. Услуги';

  @override
  String get publicServicesDescription => 'Възползвай се от услугите ни!';

  @override
  String get reports => 'Сигнали';

  @override
  String get reportsDescription => 'Бъди активен! Подай сигнал за нередност.';

  @override
  String get favorites => 'Любими';

  @override
  String get location => 'Локация';

  @override
  String get home => 'Начало';

  @override
  String get messages => 'Съобщения';

  @override
  String get emailOrUsername => 'Потребителско име или имейл';

  @override
  String get password => 'Парола';

  @override
  String get forgotPassword => 'Забравена парола?';

  @override
  String get loginButton => 'Влез';

  @override
  String get orContinueWith => '- или продължи с -';

  @override
  String get createNewAccount => 'Създай нов профил';

  @override
  String get registerHere => 'от тук';

  @override
  String get register => 'Регистрация';

  @override
  String get confirmPassword => 'Повтори парола';

  @override
  String get agreeToTerms => 'С натискането на бутона Регистрация се съгласявате с';

  @override
  String get registerTerms => 'условията за ползване';

  @override
  String get registerButton => 'Регистрация';

  @override
  String get alreadyHaveAccount => 'Имате вече съществуващ профил?';

  @override
  String get loginHere => 'Влезте от тук';

  @override
  String get dontHaveAccount => 'Нямате акаунт?';

  @override
  String get firstName => 'Име';

  @override
  String get lastName => 'Фамилия';

  @override
  String get email => 'Имейл';

  @override
  String get emailRequired => 'Имейлът е задължителен';

  @override
  String get invalidEmail => 'Моля, въведете валиден имейл';

  @override
  String get registrationSuccessful => 'Регистрацията е успешна!';

  @override
  String get passwordRequired => 'Паролата е задължителна';

  @override
  String get passwordTooShort => 'Паролата е прекалено кратка';

  @override
  String get confirmPasswordRequired => 'Потвърди паролата е задължително';

  @override
  String get passwordsDoNotMatch => 'Паролите не съвпадат';

  @override
  String get firstNameRequired => 'Името е задължително';

  @override
  String get lastNameRequired => 'Фамилията е задължителна';

  @override
  String get tourismWelcomeMessage => 'Опознай Ямбол! Разгледай забележителностите и се вдъхнови от историята му.';

  @override
  String get tourismLegendsAndMyths => 'Легенди и митове';

  @override
  String get tourismSights => 'Забележителности';

  @override
  String get tourismCulturalAndArtisticSites => 'Културни и артистични обекти';

  @override
  String get tourismRoutesAndActivities => 'Маршрути и активности';

  @override
  String get tourismFamilyEntertainment => 'Семейни забавления';

  @override
  String get tourismNightlife => 'Нощен живот';

  @override
  String get tourismTransport => 'Транспорт';

  @override
  String get tourismTravelAgencies => 'Туристически агенции';

  @override
  String get loginSuccess => 'Входът е успешен';

  @override
  String get loginError => 'Грешка при входа';

  @override
  String get legendsAndMythsWelcomeMessage => 'Открий мистериите на Ямбол: Легенди и митове, които ще те завладеят';

  @override
  String get weatherTitle => 'Времето';

  @override
  String get cityName => 'Ямбол';

  @override
  String get wind => 'Вятър';

  @override
  String get humidity => 'Влажност';

  @override
  String get errorLoadingWeather => 'Грешка при зареждане на времето';

  @override
  String get retry => 'Опитай отново';

  @override
  String get monday => 'Понеделник';

  @override
  String get tuesday => 'Вторник';

  @override
  String get wednesday => 'Сряда';

  @override
  String get thursday => 'Четвъртък';

  @override
  String get friday => 'Петък';

  @override
  String get saturday => 'Събота';

  @override
  String get sunday => 'Неделя';

  @override
  String get eventsWelcomeMessage => 'Не пропускай нищо! Разгледай културни и спортни събития в града!';

  @override
  String get search => 'Търси...';

  @override
  String get tourismCulturalSites => 'Културни и арт обекти';

  @override
  String get map => 'Карта';

  @override
  String get all => 'Всички';

  @override
  String get sportEvents => 'Спортни';

  @override
  String get cultureEvents => 'Културни';

  @override
  String get celebrationEvents => 'Празници';

  @override
  String get mon => 'ПН';

  @override
  String get tue => 'ВТ';

  @override
  String get wed => 'С';

  @override
  String get thu => 'Ч';

  @override
  String get fri => 'П';

  @override
  String get sat => 'СБ';

  @override
  String get sun => 'НД';

  @override
  String get january => 'Януари';

  @override
  String get february => 'Февруари';

  @override
  String get march => 'Март';

  @override
  String get april => 'Април';

  @override
  String get may => 'Май';

  @override
  String get june => 'Юни';

  @override
  String get july => 'Юли';

  @override
  String get august => 'Август';

  @override
  String get september => 'Септември';

  @override
  String get october => 'Октомври';

  @override
  String get november => 'Ноември';

  @override
  String get december => 'Декември';

  @override
  String get emailConfirmationTitle => 'Потвърждение на имейл';

  @override
  String get emailConfirmationMessage => 'Моля, проверете своя имейл, за да потвърдите акаунта си.';

  @override
  String get didNotReceiveEmail => 'Не сте получили имейл?';

  @override
  String get resendVerificationEmail => 'Изпрати повторно имейла за потвърждение';

  @override
  String get verificationEmailResent => 'Имейлът за потвърждение беше изпратен отново!';

  @override
  String get errorOccurred => 'Възникна грешка';

  @override
  String get emailVerificationTitle => 'Потвърждение на имейл';

  @override
  String get verifyEmailButton => 'Потвърдете имейла';

  @override
  String get emailVerifiedSuccessfully => 'Имейлът беше потвърден успешно!';

  @override
  String get generalInfoWelcomeMessage => 'Ямбол – бизнес, образование и важна информация за града!';

  @override
  String get gasStations => 'Бензиностанции';

  @override
  String get generalInformationFull => 'Обща информация';

  @override
  String get shop => 'Търговия';

  @override
  String get restaurants => 'Ресторанти';

  @override
  String get coffee => 'Кафета';

  @override
  String get bars => 'Барове';

  @override
  String get pastryShops => 'Сладкарници';

  @override
  String get establishments => 'Заведения';

  @override
  String get establishmentsWelcomeMessage => 'Уютни ресторанти, традиционна кухня и модерни заведения с неповторима атмосфера!';

  @override
  String get hotels => 'Хотели';

  @override
  String get guestHouses => 'Къщи за гости';

  @override
  String get accommodation => 'Настаняване';

  @override
  String get accommodationWelcomeMessage => 'Комфортни хотели, уютни къщи за гости и отлични места за престой!';

  @override
  String get finance => 'Финанси';

  @override
  String get banks => 'Банки';

  @override
  String get currencyExchanges => 'Обменни бюра';

  @override
  String get insuranceCompanies => 'Застрахователи';

  @override
  String get atms => 'Банкомати';

  @override
  String get financeYambolWelcomeMessage => 'Сигурни банкови услуги, изгодни кредити и финансови решения за вас в Ямбол!';

  @override
  String get bioShops => 'Био магазини';

  @override
  String get farms => 'Ферми';

  @override
  String get recycling => 'Рециклиране';

  @override
  String get ecoInitiatives => 'Еко инициативи';

  @override
  String get ecology => 'Екология';

  @override
  String get ecoYambolWelcomeMessage => 'Чиста природа, устойчиво бъдеще и екологични решения за Ямбол!';

  @override
  String get culture => 'Култура';

  @override
  String get museums => 'Музеи';

  @override
  String get theaters => 'Театри';

  @override
  String get galleries => 'Галерии';

  @override
  String get cultureYambolWelcomeMessage => 'Богато културно наследство, вдъхновяващо изкуство и традиции в Ямбол!';

  @override
  String get education => 'Образование';

  @override
  String get kindergardens => 'Детски градини';

  @override
  String get nursery => 'Детски ясли';

  @override
  String get childNutritionCenter => 'Детска кухня';

  @override
  String get schools => 'Училища';

  @override
  String get universities => 'Университети';

  @override
  String get developmentCenters => 'ЦПЛР';

  @override
  String get health => 'Здраве';

  @override
  String get pharmacies => 'Аптеки';

  @override
  String get medicalEstablishments => 'Лечебни заведения';

  @override
  String get doctorsOffices => 'Медицински кабинети';

  @override
  String get medicalLabs => 'Лаборатории';

  @override
  String get veterinaries => 'Ветеринарни клиники';

  @override
  String get healthYambolWelcomeMessage => 'Здраве и грижа за всеки – качествени медицински услуги и здравословен начин на живот в Ямбол!';

  @override
  String get educationYambolWelcomeMessage => 'Качествено образование за светло бъдеще – иновативно обучение и развитие в Ямбол!';

  @override
  String get sport => 'Спорт';

  @override
  String get sportFacilities => 'Спортни бази';

  @override
  String get sportYambolWelcomeMessage => 'Спорт и активен живот – модерни съоръжения и възможности за движение в Ямбол!';

  @override
  String get newsYambolWelcomeMessage => 'Бъди в час! Следи актуалните новини и събития.';

  @override
  String get close => 'Затвори';

  @override
  String get description => 'Описание';

  @override
  String get municipalityTaxes => 'Общински данъци';

  @override
  String get information => 'Информация';

  @override
  String get chooseWayToPay => 'Можете да платите онлайн с КИН или с електронен подпис (необходимо е той да бъде инсталиран на устройството Ви).';

  @override
  String get waysToPay => 'Начини за плащане';

  @override
  String get kinNumber => 'Плащане с КИН';

  @override
  String get kinDesc => 'Бързо и удобно плащане с вашия КИН номер';

  @override
  String get electronicSignature => 'Плащане с електронен подпис';

  @override
  String get electronicSignatureDesc => 'Сигурно плащане с вашия електронен подпис';

  @override
  String get userHelp => 'Нужда от помощ? Свържете се с нас на телефон: 0875 333 844';

  @override
  String get problemElectronicSignature => 'Не може да се отвори връзката за плащане с електронен подпис.';

  @override
  String get problemKin => 'Не може да се отвори връзката за плащане с КИН.';

  @override
  String get navigate => 'Навигирай';

  @override
  String get business => 'Бизнес';

  @override
  String get landmarks => 'Забележителности';

  @override
  String get healthcare => 'Здраве';

  @override
  String get sports => 'Спорт';

  @override
  String get applyFilters => 'Приложи филтри';

  @override
  String get filterCategories => 'Категории за филтриране';
}
