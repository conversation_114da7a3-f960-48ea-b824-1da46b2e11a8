import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:frontend/models/get_news_command.dart';

class NewsService {
  static final Dio _dio = Dio(BaseOptions(
    baseUrl: dotenv.env['BASE_URL']!, // Load BASE_URL from .env
  ));

  /// **Fetch News**
  static Future<List<NewsModel>> fetchNews({
    List<String>? categories,
    String? search,
    String? languageCode,
    int? limit,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    const String newsEndpoint = 'News'; // Adjust endpoint name as needed

    // Build query parameters dynamically based on the inputs
    final Map<String, dynamic> queryParams = {};

    // Add categories if provided
    if (categories != null && categories.isNotEmpty) {
      queryParams['categories'] = categories;
    }

    // Add search parameter if provided
    if (search != null && search.isNotEmpty) {
      queryParams['search'] = search;
    }

    // Add language code if provided
    if (languageCode != null && languageCode.isNotEmpty) {
      queryParams['languageCode'] = languageCode;
    }

    // Add limit if provided
    if (limit != null && limit > 0) {
      queryParams['limit'] = limit;
    }

    // Add date range filters if provided
    if (fromDate != null) {
      queryParams['fromDate'] = fromDate.toIso8601String();
    }

    if (toDate != null) {
      queryParams['toDate'] = toDate.toIso8601String();
    }

    try {
      print('NEWS REQUEST PARAMS: $queryParams');

      final response =
          await _dio.get(newsEndpoint, queryParameters: queryParams);

      if (response.statusCode == 200) {
        print('NEWS RESPONSE: ${response.data}');

        List<dynamic> jsonData = response.data;
        return jsonData.map((item) => NewsModel.fromJson(item)).toList();
      } else {
        throw Exception(
            'Failed to load news with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ News Service Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      return [];
    } catch (e) {
      print("⚠ News Service Unexpected Error: $e");
      return [];
    }
  }

  /// **Fetch Single News Article by ID** (if your API supports it)
  static Future<NewsModel?> fetchNewsById(String id) async {
    final String newsEndpoint = 'news/$id';

    try {
      final response = await _dio.get(newsEndpoint);

      if (response.statusCode == 200) {
        return NewsModel.fromJson(response.data);
      } else {
        throw Exception(
            'Failed to load news article with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ News Service Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      return null;
    } catch (e) {
      print("⚠ News Service Unexpected Error: $e");
      return null;
    }
  }
}
