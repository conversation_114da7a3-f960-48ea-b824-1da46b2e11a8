import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/providers/auth_providers.dart';

class EmailConfirmationPage extends ConsumerWidget {
  final String email;
  const EmailConfirmationPage({Key? key, required this.email})
      : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authNotifierProvider);
    final authNotifier = ref.read(authNotifierProvider.notifier);
    final l10n = S.of(context);

    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(Icons.email, size: 80, color: Colors.blue),
            SizedBox(height: 20),
            Text(
              l10n.emailConfirmationMessage,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18),
            ),
            SizedBox(height: 20),
            Text(
              l10n.didNotReceiveEmail,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10),
            ElevatedButton(
              onPressed: authState is AsyncLoading
                  ? null
                  : () async {
                      await authNotifier.resendVerificationEmail(email);
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text(l10n.verificationEmailResent)),
                      );
                    },
              child: authState is AsyncLoading
                  ? CircularProgressIndicator(color: Colors.white)
                  : Text(l10n.resendVerificationEmail),
            ),
            if (authState is AsyncError) ...[
              SizedBox(height: 10),
              Text(
                l10n.errorOccurred,
                style:
                    TextStyle(color: Colors.red, fontWeight: FontWeight.bold),
              ),
            ]
          ],
        ),
      ),
    );
  }
}
