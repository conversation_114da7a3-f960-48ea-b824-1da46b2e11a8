// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'interest_points_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchInterestPointsHash() =>
    r'66d99bf2af522ba81fb9a207ab41afa09a23fac8';

/// See also [fetchInterestPoints].
@ProviderFor(fetchInterestPoints)
final fetchInterestPointsProvider =
    AutoDisposeFutureProvider<List<InterestPointsGetModel>>.internal(
  fetchInterestPoints,
  name: r'fetchInterestPointsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$fetchInterestPointsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchInterestPointsRef
    = AutoDisposeFutureProviderRef<List<InterestPointsGetModel>>;
String _$interestPointFiltersHash() =>
    r'd30033a2aa7ff80d68790f30f5c6e50de331d3d9';

/// See also [InterestPointFilters].
@ProviderFor(InterestPointFilters)
final interestPointFiltersProvider = AutoDisposeNotifierProvider<
    InterestPointFilters, InterestPointFiltersState>.internal(
  InterestPointFilters.new,
  name: r'interestPointFiltersProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$interestPointFiltersHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$InterestPointFilters = AutoDisposeNotifier<InterestPointFiltersState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
