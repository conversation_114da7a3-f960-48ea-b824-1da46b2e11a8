import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/presentation/widgets/image_carousel.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:google_fonts/google_fonts.dart';

class NewsDetailsScreen extends ConsumerStatefulWidget {
  final Map<String, dynamic> newsItem;

  const NewsDetailsScreen({super.key, required this.newsItem});

  @override
  ConsumerState<NewsDetailsScreen> createState() => _NewsDetailsScreenState();
}

class _NewsDetailsScreenState extends ConsumerState<NewsDetailsScreen> {
  bool _isImagesLoaded = false;
  final List<String> _imageUrls = [];

  // Function to remove HTML tags from the description text
  String removeHtmlTags(String htmlText) {
    final RegExp exp =
        RegExp(r'<[^>]*>', multiLine: true, caseSensitive: false);
    return htmlText.replaceAll(exp, '');
  }

  // Function to preload images
  Future<void> _preloadImages(List<String> urls) async {
    if (urls.isEmpty) {
      setState(() {
        _isImagesLoaded = true;
      });
      return;
    }

    int loadedCount = 0;

    for (final url in urls) {
      final image = Image.network(url);
      final imageProvider = image.image;

      imageProvider.resolve(const ImageConfiguration()).addListener(
            ImageStreamListener((_, __) {
              loadedCount++;
              if (loadedCount == urls.length) {
                if (mounted) {
                  setState(() {
                    print('Loaded');
                    _isImagesLoaded = true;
                  });
                }
              }
            }, onError: (_, __) {
              loadedCount++;
              if (loadedCount == urls.length) {
                if (mounted) {
                  setState(() {
                    _isImagesLoaded = true;
                  });
                }
              }
            }),
          );
    }
  }

  @override
  void initState() {
    super.initState();

    // Process images from the newsItem directly
    if (widget.newsItem['imageUrls'] != null) {
      _imageUrls.addAll(List<String>.from(widget.newsItem['imageUrls']));
      _preloadImages(_imageUrls);
    } else {
      setState(() {
        _isImagesLoaded = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final themeMode = ref.watch(themeNotifierProvider);
    //final languageCode = ref.watch(languageNotifierProvider).languageCode;
    final isDarkMode = themeMode == ThemeMode.dark;

    // Access news data directly from widget.newsItem
    final title = widget.newsItem['heading'] ?? "Untitled News";
    final description =
        widget.newsItem['description'] ?? "No description available.";
    final date = widget.newsItem['date'] ?? "14.01.2025 г.";

    // Clean description text
    final cleanDescription = removeHtmlTags(description);

    // Check if we have images
    final hasImages = _imageUrls.isNotEmpty;

    // Show loading indicator until all images are loaded
    if (!_isImagesLoaded) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // Once everything is loaded, show the content
    return Scaffold(
      body: Column(
        children: [
          // Image Carousel with Date - Only if we have images
          if (hasImages)
            Stack(
              children: [
                // Image carousel
                ImageCarousel(imageUrls: _imageUrls),

                // Date container at the top-right of the image
                Positioned(
                  top: 20,
                  right: 20,
                  child: Container(
                    width: 101,
                    height: 29,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(50),
                        bottomLeft: Radius.circular(50),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.25),
                          offset: Offset(0, 0),
                          blurRadius: 10,
                        ),
                      ],
                    ),
                    child: Center(
                      child: Text(
                        date,
                        style: GoogleFonts.roboto(
                          fontWeight: FontWeight.w700,
                          fontSize: 12.5,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),

          // Heading Section
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: hasImages ? 12.0 : 32.0,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: GoogleFonts.roboto(
                      fontWeight: FontWeight.w700,
                      fontSize: 22,
                      color:
                          isDarkMode ? Colors.white : const Color(0xFF424242),
                    ),
                    textAlign: TextAlign.left,
                  ),
                ),
                // Date display when no images are present
                if (!hasImages)
                  Container(
                    width: 101,
                    height: 29,
                    margin: EdgeInsets.only(left: 8, top: 4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(50),
                        bottomLeft: Radius.circular(50),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.25),
                          offset: Offset(0, 0),
                          blurRadius: 10,
                        ),
                      ],
                    ),
                    child: Center(
                      child: Text(
                        date,
                        style: GoogleFonts.roboto(
                          fontWeight: FontWeight.w700,
                          fontSize: 12.5,
                          color: Colors.black,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Divider for visual separation
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Divider(
              thickness: 1.2,
              color: isDarkMode ? Colors.white24 : Colors.black12,
            ),
          ),

          // Description Section
          Expanded(
            child: Center(
              child: SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16.0, vertical: 10),
                child: Container(
                  width: double.infinity,
                  constraints: BoxConstraints(
                    minHeight: hasImages ? 200 : 300,
                  ),
                  decoration: BoxDecoration(
                    color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: isDarkMode
                            ? Colors.black.withOpacity(0.3)
                            : Colors.black.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  margin: EdgeInsets.only(
                    bottom: 16,
                    top: hasImages ? 0 : 16,
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          cleanDescription,
                          style: GoogleFonts.roboto(
                            fontWeight: FontWeight.w400,
                            fontSize: 15,
                            height: 1.6,
                            letterSpacing: 0.3,
                            color: isDarkMode
                                ? const Color(0xFFE0E0E0)
                                : const Color(0xFF303030),
                          ),
                          textAlign: TextAlign.start,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
