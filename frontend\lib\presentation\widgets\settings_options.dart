import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../../l10n/generated/l10n.dart';
import '../screens/register_screen.dart';

class SettingsOptions extends ConsumerWidget {
  const SettingsOptions({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeNotifier = ref.read(themeNotifierProvider.notifier);
    final themeMode = ref.watch(themeNotifierProvider);
    final langNotifier = ref.read(languageNotifierProvider.notifier);
    final currentLocale = ref.watch(languageNotifierProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          _buildSettingsTile(
            context,
            title: S.of(context).language,
            subtitle: _getLanguageName(currentLocale.languageCode),
            icon: Icons.language,
            onTap: () => _showLanguagePicker(context, langNotifier),
          ),
          const SizedBox(height: 10),

          _buildSettingsTile(
            context,
            title: S.of(context).darkMode,
            icon: themeMode == ThemeMode.dark
                ? Icons.dark_mode
                : Icons.light_mode,
            trailing: Switch(
              value: themeMode == ThemeMode.dark,
              onChanged: (value) => themeNotifier.toggleTheme(),
            ),
          ),
          const SizedBox(height: 20),

          // ✅ Register Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                context.push('/registration');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
              ),
              child: Text(
                S.of(context).registerButton,
                style: const TextStyle(fontSize: 16, color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// **Reusable Settings Tile**
  Widget _buildSettingsTile(
    BuildContext context, {
    required String title,
    String? subtitle,
    required IconData icon,
    VoidCallback? onTap,
    Widget? trailing,
  }) {
    final theme = Theme.of(context);

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        leading: Icon(icon, color: theme.primaryColor),
        title: Text(title,
            style: TextStyle(
                fontSize: 16, color: theme.textTheme.bodyLarge?.color)),
        subtitle: subtitle != null
            ? Text(subtitle,
                style: TextStyle(color: theme.textTheme.bodySmall?.color))
            : null,
        trailing:
            trailing ?? const Icon(Icons.chevron_right, color: Colors.black45),
        onTap: onTap,
      ),
    );
  }

  /// **Language Picker Modal**
  void _showLanguagePicker(
      BuildContext context, LanguageNotifier langNotifier) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                S.of(context).language,
                style:
                    const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),
              ListTile(
                title: const Text("English"),
                onTap: () {
                  langNotifier.setLanguage(const Locale('en'));
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: const Text("Български"),
                onTap: () {
                  langNotifier.setLanguage(const Locale('bg'));
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// **Get Language Name Based on Locale**
  String _getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'bg':
        return 'Български';
      default:
        return 'Unknown';
    }
  }
}
