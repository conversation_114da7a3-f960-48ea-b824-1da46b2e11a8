import 'package:flutter/material.dart';
import '../../l10n/generated/l10n.dart'; // ✅ Import localization

class FeatureTile extends StatelessWidget {
  final IconData icon;
  final String translationKey;
  final String? descriptionKey;
  final Color color;
  final bool isGridView;
  final bool disabled;

  const FeatureTile(
      {Key? key,
      required this.icon,
      required this.translationKey,
      this.descriptionKey,
      required this.color,
      this.isGridView = true,
      required this.disabled})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context); // ✅ Fetch current theme

    return isGridView
        ? _buildGridTile(context, theme)
        : _buildColumnTile(context, theme);
  }

  // ✅ Grid View Tile - Dark Mode Support
  Widget _buildGridTile(BuildContext context, ThemeData theme) {
    final isDark = theme.brightness == Brightness.dark;

    final backgroundColor = disabled
        ? (isDark ? Colors.grey[850] : Colors.grey[300])
        : (isDark ? Colors.grey[900] : Colors.white);

    final contentColor = disabled
        ? (isDark ? Colors.grey[500] : Colors.grey[600])
        : (isDark ? Colors.white : Colors.black);

    final barColor = disabled ? Colors.grey[500] : color;

    return Container(
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: isDark ? Colors.black26 : Colors.black12,
            blurRadius: 4,
            offset: const Offset(2, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 40,
            color: contentColor,
          ),
          const SizedBox(height: 8),
          Text(
            _getTranslatedText(context, translationKey),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: contentColor,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            height: 9,
            width: 100,
            decoration: BoxDecoration(
              color: barColor,
              borderRadius: BorderRadius.circular(1),
            ),
          ),
        ],
      ),
    );
  }

  // ✅ Column View Tile - Dark Mode Support
  Widget _buildColumnTile(BuildContext context, ThemeData theme) {
    final isDark = theme.brightness == Brightness.dark;

    final backgroundColor = disabled
        ? (isDark ? Colors.grey[850] : Colors.grey[300])
        : (isDark ? Colors.grey[900] : Colors.white);

    final contentColor = disabled
        ? (isDark ? Colors.grey[500] : Colors.grey[600])
        : (isDark ? Colors.white : Colors.black);

    final descriptionColor = disabled
        ? (isDark ? Colors.grey[600] : Colors.grey[500])
        : (isDark ? Colors.white70 : Colors.black54);

    final barColor = disabled ? Colors.grey[500]! : color;

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: isDark ? Colors.black26 : Colors.black12,
            blurRadius: 4,
            offset: const Offset(2, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 9,
            height: 73,
            color: barColor,
            margin: const EdgeInsets.only(right: 12),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(icon, size: 30, color: contentColor),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _getTranslatedText(context, translationKey),
                        textAlign: TextAlign.start,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                          color: contentColor,
                        ),
                      ),
                    ),
                  ],
                ),
                if (descriptionKey != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    _getTranslatedText(context, descriptionKey!),
                    style: TextStyle(
                      fontSize: 14,
                      color: descriptionColor,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// **Fetches translated text based on the provided key**
  String _getTranslatedText(BuildContext context, String key) {
    final translations = S.of(context);
    switch (key) {
      case 'tourism':
        return translations.tourism;
      case 'tourism_description':
        return translations.tourismDescription;
      case 'weather':
        return translations.weather;
      case 'weather_description':
        return translations.weatherDescription;
      case 'events':
        return translations.events;
      case 'events_description':
        return translations.eventsDescription;
      case 'repairs':
        return translations.repairs;
      case 'repairs_description':
        return translations.repairsDescription;
      case 'parking':
        return translations.parking;
      case 'parking_description':
        return translations.parkingDescription;
      case 'general_info':
        return translations.generalInfo;
      case 'general_info_description':
        return translations.generalInfoDescription;
      case 'transport':
        return translations.transport;
      case 'transport_description':
        return translations.transportDescription;
      case 'cameras':
        return translations.cameras;
      case 'cameras_description':
        return translations.camerasDescription;
      case 'news':
        return translations.news;
      case 'news_description':
        return translations.newsDescription;
      case 'taxes':
        return translations.taxes;
      case 'taxes_description':
        return translations.taxesDescription;
      case 'public_services':
        return translations.publicServices;
      case 'public_services_description':
        return translations.publicServicesDescription;
      case 'reports':
        return translations.reports;
      case 'reports_description':
        return translations.reportsDescription;
      default:
        return key; // Fallback if translation is missing
    }
  }
}
