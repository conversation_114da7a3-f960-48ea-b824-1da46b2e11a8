import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:frontend/models/get_events_by_id_command.dart';
import 'package:frontend/models/get_events_command.dart';

class EventsMobileService {
  static final Dio _dio = Dio(BaseOptions(
    baseUrl: dotenv.env['BASE_URL']!, // Load BASE_URL from .env
  ));

  /// **Fetch Events**
  static Future<List<EventsGetModel>> fetchEvents({
    List<String>? categories, // Categories filter
    String? search,
    String? languageCode,
    String? fromDate, // Optional date range filters
    String? toDate,
  }) async {
    const String eventsEndpoint = 'Events/Mobile'; // Events/Mobile endpoint

    // Build query parameters dynamically based on the inputs
    final Map<String, dynamic> queryParams = {};

    // Check if categories is not null or empty and then add to queryParams
    if (categories != null && categories.isNotEmpty) {
      queryParams['category'] = categories.first;
    }
    if (search != null && search.isNotEmpty) {
      queryParams['search'] = search;
    }
    if (languageCode != null && languageCode.isNotEmpty) {
      queryParams['languageCode'] = languageCode;
    }
    if (fromDate != null && fromDate.isNotEmpty) {
      queryParams['fromDate'] = fromDate;
    }
    if (toDate != null && toDate.isNotEmpty) {
      queryParams['toDate'] = toDate;
    }

    try {
      print('EVENTS PARAMS FOR REQUEST $queryParams');

      final response =
          await _dio.get(eventsEndpoint, queryParameters: queryParams);

      if (response.statusCode == 200) {
        print('Events Response: ${response.data}');
        List<dynamic> jsonData = response.data;
        return jsonData.map((event) => EventsGetModel.fromJson(event)).toList();
      } else {
        throw Exception(
            'Failed to load events with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ Events Dio Error: ${e.response?.statusCode}");
      print("📩 Events Response Data: ${e.response?.data}");
      print("📝 Events Error Message: ${e.message}");
      return [];
    } catch (e) {
      print("⚠ Events Unexpected Error: $e");
      return [];
    }
  }

  /// **Fetch Event Details by ID**
  static Future<EventsDetailsModel?> fetchEventById(int eventId) async {
    final String eventDetailsEndpoint =
        'Events/Mobile/$eventId'; // Events/Mobile/{id} endpoint

    try {
      print('FETCHING EVENT DETAILS FOR ID: $eventId');

      final response = await _dio.get(eventDetailsEndpoint);

      if (response.statusCode == 200) {
        print('Event Details Response: ${response.data}');
        return EventsDetailsModel.fromJson(response.data);
      } else {
        throw Exception(
            'Failed to load event details with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ Event Details Dio Error: ${e.response?.statusCode}");
      print("📩 Event Details Response Data: ${e.response?.data}");
      print("📝 Event Details Error Message: ${e.message}");
      return null;
    } catch (e) {
      print("⚠ Event Details Unexpected Error: $e");
      return null;
    }
  }

  /// **Fetch Events by Category** (Helper method for specific categories)
  static Future<List<EventsGetModel>> fetchEventsByCategory({
    required String category,
    String? search,
    String? languageCode,
    String? fromDate,
    String? toDate,
  }) async {
    return await fetchEvents(
      categories: [category],
      search: search,
      languageCode: languageCode,
      fromDate: fromDate,
      toDate: toDate,
    );
  }

  /// **Fetch Sports Events** (Convenience method for Sports category)
  static Future<List<EventsGetModel>> fetchSportsEvents({
    String? search,
    String? languageCode,
    String? fromDate,
    String? toDate,
  }) async {
    return await fetchEventsByCategory(
      category: 'Sports',
      search: search,
      languageCode: languageCode,
      fromDate: fromDate,
      toDate: toDate,
    );
  }
}
