class InterestPointsGetModel {
  final int id;
  final String name;
  final double? latitude; // Nullable double for latitude
  final double? longitude; // Nullable double for longitude
  final bool? isFavourite;
  final String category;

  InterestPointsGetModel(
      {required this.id,
      required this.name,
      this.latitude,
      this.longitude,
      this.isFavourite,
      required this.category});

  // Factory constructor to create an Item from a JSON map
  factory InterestPointsGetModel.fromJson(Map<String, dynamic> json) {
    return InterestPointsGetModel(
      id: json['id'],
      name: json['name'],
      latitude: json['latitude'] != null
          ? (json['latitude'] as num).toDouble()
          : null, // Convert to double if not null
      longitude: json['longitude'] != null
          ? (json['longitude'] as num).toDouble()
          : null, // Convert to double if not null
      isFavourite:
          json['isFavourite'] == null ? null : json['isFavourite'] as bool?,
      category: json['category'],
    );
  }

  // Method to convert an Item to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'latitude': latitude, // Optional field
      'longitude': longitude, // Optional field
      'isFavourite': isFavourite, // Optional field
      'category': category
    };
  }

  @override
  String toString() {
    return 'InterestPointsGetModel(id: $id, name: $name, latitude: $latitude, longitude: $longitude, isFavourite: $isFavourite, category: $category)';
  }
}
