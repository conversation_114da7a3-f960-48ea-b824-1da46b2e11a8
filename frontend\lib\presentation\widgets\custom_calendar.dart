import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/providers/calendar_provider.dart';

class CalendarWidget extends ConsumerWidget {
  const CalendarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final calendarState = ref.watch(calendarNotifierProvider);
    final calendarNotifier = ref.read(calendarNotifierProvider.notifier);

    final displayedMonth = calendarState.displayedMonth;
    final selectedDate = calendarState.selectedDate;

    final currentMonth = displayedMonth.month;
    final currentYear = displayedMonth.year;
    final daysInMonth = DateTime(currentYear, currentMonth + 1, 0).day;
    final firstDayOfMonth = DateTime(currentYear, currentMonth, 1).weekday;

    // Adjust for Monday as the first day of the week
    final startingPos = firstDayOfMonth - 1;

    return Container(
      width: 319,
      height: 323,
      decoration: BoxDecoration(
        color: Theme.of(context)
            .cardColor
            .withValues(alpha: 0.95), // Slightly transparent for overlay effect
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 5,
            spreadRadius: 2,
          ),
        ],
      ),
      padding: EdgeInsets.all(15),
      child: Column(
        children: [
          // Month navigation and header - FIXED
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: Icon(Icons.chevron_left),
                onPressed: calendarNotifier.previousMonth,
                padding: EdgeInsets.zero,
                constraints: BoxConstraints(),
                iconSize: 24,
                color: Theme.of(context).iconTheme.color,
              ),
              GestureDetector(
                onTap: calendarNotifier.resetToCurrentMonth,
                child: Text(
                  '${_getMonthName(context, currentMonth)} $currentYear',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                    color: Theme.of(context).textTheme.bodyLarge?.color,
                  ),
                ),
              ),
              IconButton(
                icon: Icon(Icons.chevron_right),
                onPressed: calendarNotifier.nextMonth,
                padding: EdgeInsets.zero,
                constraints: BoxConstraints(),
                iconSize: 24,
                color: Theme.of(context).iconTheme.color,
              ),
            ],
          ),
          SizedBox(height: 10),

          // Days of the week - FIXED
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              for (var day in [
                S.of(context).mon,
                S.of(context).tue,
                S.of(context).wed,
                S.of(context).thu,
                S.of(context).fri,
                S.of(context).sat,
                S.of(context).sun
              ])
                Text(
                  day,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context)
                        .textTheme
                        .bodyMedium
                        ?.color
                        ?.withValues(alpha: 0.7),
                  ),
                ),
            ],
          ),
          SizedBox(height: 8),

          // ONLY the Calendar grid is scrollable now
          Expanded(
            child: SingleChildScrollView(
              physics: ClampingScrollPhysics(),
              child: GridView.builder(
                shrinkWrap: true,
                physics:
                    NeverScrollableScrollPhysics(), // Disable GridView's scroll
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 7,
                  childAspectRatio: 1,
                ),
                itemCount: 42, // 6 rows * 7 columns
                itemBuilder: (context, index) {
                  final dayNumber = index - startingPos + 1;
                  if (dayNumber < 1 || dayNumber > daysInMonth) {
                    return Container(); // Empty spaces
                  }

                  final isSelected = selectedDate != null &&
                      selectedDate.day == dayNumber &&
                      selectedDate.month == currentMonth &&
                      selectedDate.year == currentYear;

                  return GestureDetector(
                    onTap: () {
                      calendarNotifier.selectDate(
                        DateTime(currentYear, currentMonth, dayNumber),
                      );
                    },
                    child: Container(
                      margin: EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: isSelected ? Color(0xFF22D400) : null,
                      ),
                      child: Center(
                        child: Text(
                          '$dayNumber',
                          style: TextStyle(
                            fontWeight: isSelected
                                ? FontWeight.bold
                                : FontWeight.normal,
                            fontSize: 16,
                            color: isSelected
                                ? Colors.white
                                : Theme.of(context).textTheme.bodyMedium?.color,
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getMonthName(BuildContext context, int month) {
    final months = [
      S.of(context).january, // Use localization correctly
      S.of(context).february,
      S.of(context).march,
      S.of(context).april,
      S.of(context).may,
      S.of(context).june,
      S.of(context).july,
      S.of(context).august,
      S.of(context).september,
      S.of(context).october,
      S.of(context).november,
      S.of(context).december
    ];

    return months[month - 1]; // Ensure the index is within bounds
  }
}
