// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'interest_points_details.provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchInterestPointDetailsHash() =>
    r'f4f8f5d5e6f932a4c227ba6810ffbaf395536264';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchInterestPointDetails].
@ProviderFor(fetchInterestPointDetails)
const fetchInterestPointDetailsProvider = FetchInterestPointDetailsFamily();

/// See also [fetchInterestPointDetails].
class FetchInterestPointDetailsFamily
    extends Family<AsyncValue<InterestPointDetailsModel>> {
  /// See also [fetchInterestPointDetails].
  const FetchInterestPointDetailsFamily();

  /// See also [fetchInterestPointDetails].
  FetchInterestPointDetailsProvider call(
    int id,
    String languageCode,
  ) {
    return FetchInterestPointDetailsProvider(
      id,
      languageCode,
    );
  }

  @override
  FetchInterestPointDetailsProvider getProviderOverride(
    covariant FetchInterestPointDetailsProvider provider,
  ) {
    return call(
      provider.id,
      provider.languageCode,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchInterestPointDetailsProvider';
}

/// See also [fetchInterestPointDetails].
class FetchInterestPointDetailsProvider
    extends AutoDisposeFutureProvider<InterestPointDetailsModel> {
  /// See also [fetchInterestPointDetails].
  FetchInterestPointDetailsProvider(
    int id,
    String languageCode,
  ) : this._internal(
          (ref) => fetchInterestPointDetails(
            ref as FetchInterestPointDetailsRef,
            id,
            languageCode,
          ),
          from: fetchInterestPointDetailsProvider,
          name: r'fetchInterestPointDetailsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchInterestPointDetailsHash,
          dependencies: FetchInterestPointDetailsFamily._dependencies,
          allTransitiveDependencies:
              FetchInterestPointDetailsFamily._allTransitiveDependencies,
          id: id,
          languageCode: languageCode,
        );

  FetchInterestPointDetailsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
    required this.languageCode,
  }) : super.internal();

  final int id;
  final String languageCode;

  @override
  Override overrideWith(
    FutureOr<InterestPointDetailsModel> Function(
            FetchInterestPointDetailsRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchInterestPointDetailsProvider._internal(
        (ref) => create(ref as FetchInterestPointDetailsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
        languageCode: languageCode,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<InterestPointDetailsModel> createElement() {
    return _FetchInterestPointDetailsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchInterestPointDetailsProvider &&
        other.id == id &&
        other.languageCode == languageCode;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);
    hash = _SystemHash.combine(hash, languageCode.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchInterestPointDetailsRef
    on AutoDisposeFutureProviderRef<InterestPointDetailsModel> {
  /// The parameter `id` of this provider.
  int get id;

  /// The parameter `languageCode` of this provider.
  String get languageCode;
}

class _FetchInterestPointDetailsProviderElement
    extends AutoDisposeFutureProviderElement<InterestPointDetailsModel>
    with FetchInterestPointDetailsRef {
  _FetchInterestPointDetailsProviderElement(super.provider);

  @override
  int get id => (origin as FetchInterestPointDetailsProvider).id;
  @override
  String get languageCode =>
      (origin as FetchInterestPointDetailsProvider).languageCode;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
