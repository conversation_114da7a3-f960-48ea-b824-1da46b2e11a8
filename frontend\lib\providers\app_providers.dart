import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'app_providers.g.dart';

@riverpod
class SelectedIndex extends _$SelectedIndex {
  @override
  int build() => 2;

  void setIndex(int index) => state = index;
}

// Provider to track Grid or Column view
@riverpod
class LayoutMode extends _$LayoutMode {
  @override
  bool build() => true; // Default: true (GridView)

  void toggleLayout() => state = !state;
}
