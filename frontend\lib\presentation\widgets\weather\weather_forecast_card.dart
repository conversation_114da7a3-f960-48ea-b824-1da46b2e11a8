import 'package:flutter/material.dart';
import 'package:frontend/infrastructure/weather/weather_icon_service.dart';

class DailyForecastCard extends StatelessWidget {
  final double minTemp;
  final double maxTemp;
  final int weatherCode;
  final WeatherIconService iconService;
  final String day;

  const DailyForecastCard({
    required this.minTemp,
    required this.maxTemp,
    required this.weatherCode,
    required this.iconService,
    required this.day,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.3),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            iconService.showWeatherIcon(weatherCode),
            color: Colors.white,
            size: 50,
          ),
          SizedBox(height: 12),
          Text(
            day,
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 6),
          Text(
            '${minTemp.toStringAsFixed(1)}°C - ${maxTemp.toStringAsFixed(1)}°C',
            style: TextStyle(
              color: Colors.white.withOpacity(0.9),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
