class NewsModel {
  final String title;
  final String summary;
  final String link;
  final DateTime publishedDate;

  NewsModel({
    required this.title,
    required this.summary,
    required this.link,
    required this.publishedDate,
  });

  factory NewsModel.fromJson(Map<String, dynamic> json) {
    return NewsModel(
      title: json['title'] ?? '',
      summary: json['summary'] ?? '',
      link: json['link'] ?? '',
      publishedDate: DateTime.parse(json['publishedDate']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'summary': summary,
      'link': link,
      'publishedDate': publishedDate.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'NewsModel(title: $title, summary: $summary, link: $link, publishedDate: $publishedDate)';
  }
}
