import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:frontend/infrastructure/navigation/navigation_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:go_router/go_router.dart';

part 'deep_link_provider.g.dart';

@riverpod
class DeepLink extends _$DeepLink {
  late final AppLinks _appLinks;

  @override
  Future<Uri?> build() async {
    _appLinks = AppLinks();

    // Get initial deep link if the app was opened via a link
    final Uri? initialLink = await _appLinks.getInitialLink();

    if (initialLink != null) {
      _handleDeepLink(initialLink);
      return initialLink;
    }

    // Listen for deep links while app is running
    _appLinks.uriLinkStream.listen((Uri? uri) {
      if (uri != null) {
        _handleDeepLink(uri);
      }
    }, onError: (err) {
      debugPrint("Deep link error: $err");
    });

    return null;
  }

  void _handleDeepLink(Uri uri) {
    debugPrint("Received deep link: ${uri.toString()}");

    if (uri.path == '/Users/<USER>') {
      final userId = uri.queryParameters['userId'];
      final token = uri.queryParameters['token'];

      if (userId != null && token != null) {
        // ✅ Navigate using GoRouter with the global navigator key
        globalNavigatorKey.currentState?.context.go(
          '/confirm-email?userId=$userId&token=$token',
        );
      }
    }
  }
}
