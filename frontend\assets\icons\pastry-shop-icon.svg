<svg width="28" height="36" viewBox="0 0 28 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_740_3717)">
<ellipse cx="12" cy="29" rx="4" ry="2" fill="black" fill-opacity="0.12"/>
</g>
<mask id="path-2-outside-1_740_3717" maskUnits="userSpaceOnUse" x="1.33301" y="1.25" width="26" height="32" fill="black">
<rect fill="white" x="1.33301" y="1.25" width="26" height="32"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.9016 22.5711C23.791 20.5234 25.6663 17.2229 25.6663 13.5C25.6663 7.2868 20.443 2.25 13.9997 2.25C7.55635 2.25 2.33301 7.2868 2.33301 13.5C2.33301 17.2229 4.20832 20.5234 7.09774 22.5711C9.28849 24.1629 12.3745 26.672 13.0951 30.7071C13.1745 31.1518 13.5479 31.4966 13.9997 31.4966C14.4514 31.4966 14.8249 31.1518 14.9043 30.7071C15.6248 26.6719 18.7109 24.1629 20.9016 22.5711Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.9016 22.5711C23.791 20.5234 25.6663 17.2229 25.6663 13.5C25.6663 7.2868 20.443 2.25 13.9997 2.25C7.55635 2.25 2.33301 7.2868 2.33301 13.5C2.33301 17.2229 4.20832 20.5234 7.09774 22.5711C9.28849 24.1629 12.3745 26.672 13.0951 30.7071C13.1745 31.1518 13.5479 31.4966 13.9997 31.4966C14.4514 31.4966 14.8249 31.1518 14.9043 30.7071C15.6248 26.6719 18.7109 24.1629 20.9016 22.5711Z" fill="#800080"/>
<path d="M20.9016 22.5711L20.3234 21.7551L20.3138 21.7621L20.9016 22.5711ZM7.09774 22.5711L7.6856 21.762L7.67595 21.7552L7.09774 22.5711ZM13.0951 30.7071L12.1106 30.8829L12.1106 30.8829L13.0951 30.7071ZM14.9043 30.7071L15.8887 30.8829L15.8887 30.8829L14.9043 30.7071ZM21.4799 23.3869C24.6146 21.1654 26.6663 17.57 26.6663 13.5H24.6663C24.6663 16.8757 22.9675 19.8814 20.3234 21.7552L21.4799 23.3869ZM26.6663 13.5C26.6663 6.70055 20.9607 1.25 13.9997 1.25V3.25C19.9253 3.25 24.6663 7.87305 24.6663 13.5H26.6663ZM13.9997 1.25C7.03866 1.25 1.33301 6.70055 1.33301 13.5H3.33301C3.33301 7.87305 8.07405 3.25 13.9997 3.25V1.25ZM1.33301 13.5C1.33301 17.57 3.38476 21.1654 6.51953 23.387L7.67595 21.7552C5.03189 19.8814 3.33301 16.8758 3.33301 13.5H1.33301ZM14.0795 30.5313C13.2833 26.0726 9.88416 23.3596 7.68556 21.7621L6.50992 23.3801C8.69282 24.9662 11.4657 27.2714 12.1106 30.8829L14.0795 30.5313ZM13.9997 30.4966C14.0152 30.4966 14.0312 30.4997 14.0451 30.5052C14.0584 30.5105 14.0669 30.5166 14.0715 30.5207C14.076 30.5246 14.0778 30.5275 14.0784 30.5287C14.0792 30.53 14.0794 30.5309 14.0795 30.5313L12.1106 30.8829C12.2663 31.7544 13.0143 32.4966 13.9997 32.4966V30.4966ZM13.9199 30.5313C13.9199 30.5309 13.9202 30.53 13.9209 30.5287C13.9216 30.5275 13.9234 30.5246 13.9278 30.5207C13.9325 30.5166 13.9409 30.5105 13.9542 30.5052C13.9682 30.4997 13.9842 30.4966 13.9997 30.4966V32.4966C14.9851 32.4966 15.7331 31.7544 15.8887 30.8829L13.9199 30.5313ZM20.3138 21.7621C18.1152 23.3596 14.716 26.0726 13.9199 30.5313L15.8887 30.8829C16.5336 27.2713 19.3066 24.9662 21.4895 23.38L20.3138 21.7621Z" fill="url(#paint0_linear_740_3717)" mask="url(#path-2-outside-1_740_3717)"/>
<path d="M14.989 9.04264C15.4038 9.94833 15.1614 11.2655 14.2999 11.8218C13.3543 12.4324 12.0928 12.1608 11.4823 11.2152C11.418 11.1154 11.3625 11.0103 11.3165 10.9009L6 13.2336V18.1031H20.0605V13.8344C19.8232 13.6612 19.5988 13.3696 19.5164 12.9398C19.2493 12.7669 19.0535 12.5035 18.9651 12.1978C18.5154 12.1002 18.1464 11.7784 17.9918 11.3484C17.478 11.2534 17.0645 10.8664 16.9386 10.3632C16.5186 10.2446 16.2493 9.99286 16.1089 9.58848C15.7454 9.60583 15.343 9.44567 14.989 9.04264ZM19.1447 16.9957H6.87963V16.2465H19.1447V16.9957ZM19.1447 14.4992H6.87963V13.7189H19.1447V14.4992ZM14.0828 8.90636C13.9347 8.81073 13.8048 8.74486 13.6427 8.7058C13.5985 8.51198 13.5524 8.32064 13.527 8.1947C13.3564 7.35142 12.9739 6.5302 12.2108 6.0808C11.8569 5.87061 11.3807 6.09126 11.3251 6.50308C11.2784 6.84914 11.5059 7.02567 11.777 7.16155C12.3372 7.44239 12.66 7.97805 12.8752 8.55923C12.8901 8.59945 12.9045 8.6487 12.92 8.70167C12.5468 8.7868 12.2052 9.01176 11.9811 9.35876C11.5257 10.064 11.7283 11.005 12.4335 11.4604C13.1388 11.9158 14.0798 11.7132 14.5352 11.008C14.9906 10.3027 14.788 9.36173 14.0828 8.90633M13.7147 9.96429C13.5046 9.96429 13.3343 9.79398 13.3343 9.58398C13.3343 9.37389 13.5046 9.20361 13.7147 9.20361C13.9247 9.20361 14.095 9.37392 14.095 9.58395C14.095 9.79401 13.9247 9.96429 13.7147 9.96429ZM21.2477 13.788V18.103H20.4381V13.667C20.1518 13.4865 19.9615 13.1678 19.9615 12.8044L19.9616 12.8021C19.5645 12.6885 19.2667 12.3402 19.2276 11.9169C18.7307 11.905 18.3137 11.5379 18.2378 11.0595C17.6943 11.0595 17.2422 10.6343 17.2121 10.0983C16.7817 10.0591 16.4289 9.75239 16.3208 9.34589C16.2789 9.35117 16.2367 9.35388 16.1944 9.35398C15.6315 9.35398 15.1752 8.89764 15.1752 8.3347C15.1752 7.77176 15.6315 7.31545 16.1944 7.31545C16.6666 7.31545 17.0637 7.63658 17.1794 8.07233C17.2213 8.06705 17.2635 8.06436 17.3057 8.06426C17.8492 8.06426 18.2932 8.48964 18.3232 9.02558C18.7887 9.06801 19.1719 9.42301 19.2443 9.8792C19.7752 9.8792 20.2194 10.285 20.2672 10.8035C20.8186 10.8167 21.2616 11.2675 21.2616 11.8221L21.2615 11.8246C21.6878 11.9465 22 12.3389 22 12.8044C22 13.2749 21.6811 13.6707 21.2477 13.788" fill="white"/>
<defs>
<filter id="filter0_f_740_3717" x="6" y="25" width="12" height="8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_740_3717"/>
</filter>
<linearGradient id="paint0_linear_740_3717" x1="13.9997" y1="2.25" x2="13.9997" y2="31.4966" gradientUnits="userSpaceOnUse">
<stop stop-color="#626262"/>
<stop offset="1" stop-color="#626262"/>
</linearGradient>
</defs>
</svg>
