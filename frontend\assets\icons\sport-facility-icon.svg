<svg width="28" height="36" viewBox="0 0 28 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_297_4449)">
<ellipse cx="12" cy="29" rx="4" ry="2" fill="black" fill-opacity="0.12"/>
</g>
<mask id="path-2-outside-1_297_4449" maskUnits="userSpaceOnUse" x="1.3335" y="1.25" width="26" height="32" fill="black">
<rect fill="white" x="1.3335" y="1.25" width="26" height="32"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.9021 22.5711C23.7915 20.5234 25.6668 17.2229 25.6668 13.5C25.6668 7.2868 20.4435 2.25 14.0002 2.25C7.55684 2.25 2.3335 7.2868 2.3335 13.5C2.3335 17.2229 4.20881 20.5234 7.09822 22.5711C9.28898 24.1629 12.375 26.672 13.0956 30.7071C13.175 31.1518 13.5484 31.4966 14.0002 31.4966C14.4519 31.4966 14.8254 31.1518 14.9048 30.7071C15.6253 26.6719 18.7114 24.1629 20.9021 22.5711Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.9021 22.5711C23.7915 20.5234 25.6668 17.2229 25.6668 13.5C25.6668 7.2868 20.4435 2.25 14.0002 2.25C7.55684 2.25 2.3335 7.2868 2.3335 13.5C2.3335 17.2229 4.20881 20.5234 7.09822 22.5711C9.28898 24.1629 12.375 26.672 13.0956 30.7071C13.175 31.1518 13.5484 31.4966 14.0002 31.4966C14.4519 31.4966 14.8254 31.1518 14.9048 30.7071C15.6253 26.6719 18.7114 24.1629 20.9021 22.5711Z" fill="#008DD4"/>
<path d="M20.9021 22.5711L20.3239 21.7551L20.3143 21.7621L20.9021 22.5711ZM7.09822 22.5711L7.68608 21.762L7.67643 21.7552L7.09822 22.5711ZM13.0956 30.7071L12.1111 30.8829L12.1111 30.8829L13.0956 30.7071ZM14.9048 30.7071L15.8892 30.8829L15.8892 30.8829L14.9048 30.7071ZM21.4803 23.3869C24.6151 21.1654 26.6668 17.57 26.6668 13.5H24.6668C24.6668 16.8757 22.968 19.8814 20.3239 21.7552L21.4803 23.3869ZM26.6668 13.5C26.6668 6.70055 20.9612 1.25 14.0002 1.25V3.25C19.9258 3.25 24.6668 7.87305 24.6668 13.5H26.6668ZM14.0002 1.25C7.03915 1.25 1.3335 6.70055 1.3335 13.5H3.3335C3.3335 7.87305 8.07454 3.25 14.0002 3.25V1.25ZM1.3335 13.5C1.3335 17.57 3.38525 21.1654 6.52001 23.387L7.67643 21.7552C5.03237 19.8814 3.3335 16.8758 3.3335 13.5H1.3335ZM14.08 30.5313C13.2838 26.0726 9.88465 23.3596 7.68604 21.7621L6.51041 23.3801C8.69331 24.9662 11.4662 27.2714 12.1111 30.8829L14.08 30.5313ZM14.0002 30.4966C14.0157 30.4966 14.0317 30.4997 14.0456 30.5052C14.0589 30.5105 14.0674 30.5166 14.072 30.5207C14.0765 30.5246 14.0783 30.5275 14.0789 30.5287C14.0797 30.53 14.0799 30.5309 14.08 30.5313L12.1111 30.8829C12.2667 31.7544 13.0148 32.4966 14.0002 32.4966V30.4966ZM13.9203 30.5313C13.9204 30.5309 13.9207 30.53 13.9214 30.5287C13.922 30.5275 13.9239 30.5246 13.9283 30.5207C13.9329 30.5166 13.9414 30.5105 13.9547 30.5052C13.9687 30.4997 13.9847 30.4966 14.0002 30.4966V32.4966C14.9856 32.4966 15.7336 31.7544 15.8892 30.8829L13.9203 30.5313ZM20.3143 21.7621C18.1157 23.3596 14.7165 26.0726 13.9203 30.5313L15.8892 30.8829C16.5341 27.2713 19.307 24.9662 21.49 23.38L20.3143 21.7621Z" fill="url(#paint0_linear_297_4449)" mask="url(#path-2-outside-1_297_4449)"/>
<path d="M14.0005 20.6094C17.8665 20.6094 21.0005 17.5873 21.0005 13.8594C21.0005 10.1315 17.8665 7.10938 14.0005 7.10938C10.1345 7.10938 7.00049 10.1315 7.00049 13.8594C7.00049 17.5873 10.1345 20.6094 14.0005 20.6094Z" stroke="white" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M14.0002 11.0175L11.226 13.2615M14.0002 11.0175L16.7743 13.2615M14.0002 11.0175V8.99355M11.226 13.2615L11.9585 16.1099M11.226 13.2615L9.2985 12.474M11.9585 16.1099H16.0418M11.9585 16.1099L10.9377 17.7974M16.0418 16.1099L16.7743 13.2615M16.0418 16.1099L17.0627 17.7974L16.0418 20.2935M16.7743 13.2615L18.7018 12.474M18.7018 12.474L19.6417 9.89848M18.7018 12.474L20.9637 14.351M9.2985 12.474L8.3586 9.89848M9.2985 12.474L7.03662 14.351M14.0002 8.99355L16.3335 7.49449M14.0002 8.99355L11.6668 7.49414M19.6417 17.7974H17.1356M10.9377 17.7974L11.972 20.3111M10.9377 17.7974H8.37209" stroke="white" stroke-linecap="round" stroke-linejoin="round"/>
<defs>
<filter id="filter0_f_297_4449" x="6" y="25" width="12" height="8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_297_4449"/>
</filter>
<linearGradient id="paint0_linear_297_4449" x1="14.0002" y1="2.25" x2="14.0002" y2="31.4966" gradientUnits="userSpaceOnUse">
<stop stop-color="#626262"/>
<stop offset="1" stop-color="#626262"/>
</linearGradient>
</defs>
</svg>
