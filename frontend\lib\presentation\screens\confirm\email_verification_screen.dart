import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/providers/auth_providers.dart';

class EmailVerificationScreen extends ConsumerStatefulWidget {
  final String userId;
  final String token;

  const EmailVerificationScreen({
    Key? key,
    required this.userId,
    required this.token,
  }) : super(key: key);

  @override
  ConsumerState<EmailVerificationScreen> createState() =>
      _EmailVerificationScreenState();
}

class _EmailVerificationScreenState
    extends ConsumerState<EmailVerificationScreen> {
  bool wasSubmitted = false;

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authNotifierProvider);
    final l10n = S.of(context);

    return Scaffold(
      appBar: AppBar(title: Text(l10n.emailVerificationTitle)),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (authState is AsyncLoading && wasSubmitted)
              const CircularProgressIndicator(),
            if (authState is AsyncError && wasSubmitted)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  "❌ ${l10n.errorOccurred}: ${authState.error}",
                  style: const TextStyle(fontSize: 16, color: Colors.red),
                ),
              ),
            if (authState is AsyncData && wasSubmitted)
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  "✅ ${l10n.emailVerifiedSuccessfully}",
                  style: const TextStyle(fontSize: 16, color: Colors.green),
                ),
              ),
            ElevatedButton(
              onPressed: () {
                setState(() => wasSubmitted = true);
                ref
                    .read(authNotifierProvider.notifier)
                    .verifyEmail(widget.userId, widget.token);
              },
              child: Text(l10n.verifyEmailButton),
            ),
          ],
        ),
      ),
    );
  }
}
