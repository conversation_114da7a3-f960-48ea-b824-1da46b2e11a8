import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:frontend/models/get_interest_points_command.dart';
import 'package:frontend/infrastructure/points-of-interest/interest_points_service.dart';

part 'interest_points_provider.g.dart';

// State class to hold filters
class InterestPointFiltersState {
  final List<String>? categories; // Change category to List<String>
  final String? search;
  final String languageCode;

  const InterestPointFiltersState({
    this.categories,
    this.search,
    required this.languageCode,
  });

  InterestPointFiltersState copyWith({
    List<String>? categories,
    String? search,
    String? languageCode,
  }) {
    return InterestPointFiltersState(
      categories: categories ?? this.categories,
      search: search ?? this.search,
      languageCode: languageCode ?? this.languageCode,
    );
  }
}

// Provider for managing categories, search, and languageCode parameters
@riverpod
class InterestPointFilters extends _$InterestPointFilters {
  @override
  InterestPointFiltersState build() {
    return const InterestPointFiltersState(
      languageCode: 'bg', // Default language code
    );
  }

  void setCategories(List<String>? categories) {
    state = state.copyWith(
        categories: categories?.isEmpty == true ? null : categories);
  }

  void setSearch(String? search) {
    print("Search $search");
    if (search == '') {
      state = state.copyWith(search: '');
    } else {
      state = state.copyWith(search: search?.isEmpty == true ? null : search);
    }
  }

  void setLanguageCode(String? languageCode) {
    if (languageCode == null || languageCode.isEmpty) return;
    state = state.copyWith(languageCode: languageCode);
  }

  void resetFilters() {
    state = InterestPointFiltersState(languageCode: state.languageCode);
  }
}

// Provider to fetch interest points based on filters
@riverpod
Future<List<InterestPointsGetModel>> fetchInterestPoints(Ref ref) async {
  // Watch filters to automatically refresh when they change
  final filters = ref.watch(interestPointFiltersProvider);

  // If categories are null or empty, we avoid making the request
  if (filters.categories == null || filters.categories!.isEmpty) {
    return [];
  }

  print(filters.categories);

  // Fetch data based on current filters
  return InterestPointsService.fetchItems(
    categories: filters.categories, // Pass categories as a list
    search: filters.search,
    languageCode: filters.languageCode,
  );
}
