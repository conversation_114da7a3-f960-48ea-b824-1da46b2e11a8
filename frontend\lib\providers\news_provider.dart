import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:frontend/infrastructure/news/news_service.dart';
import 'package:frontend/models/get_news_command.dart';

part 'news_provider.g.dart';

// Provider to fetch all news without filters (for initial load)
@riverpod
Future<List<NewsModel>> fetchAllNews(Ref ref) async {
  print("Fetching all news");
  return NewsService.fetchNews();
}
