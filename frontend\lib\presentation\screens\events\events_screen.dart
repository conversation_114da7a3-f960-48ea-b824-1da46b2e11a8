import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/presentation/widgets/custom_calendar.dart';
import 'package:frontend/providers/filter_provider.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:frontend/providers/view_mode_provider.dart';
import 'package:frontend/providers/events_provider.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';

class EventsScreen extends ConsumerStatefulWidget {
  const EventsScreen({super.key});

  @override
  ConsumerState<EventsScreen> createState() => _EventsScreenState();
}

class _EventsScreenState extends ConsumerState<EventsScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Set portrait orientation when this screen is initialized
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Listen to search text changes
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    // Release the orientation constraint when this screen is disposed
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    // Update search state when text changes
    ref.read(searchStateProvider.notifier).setSearch(_searchController.text);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final viewMode = ref.watch(viewModeStateProvider);
    final themeMode = ref.watch(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;
    final viewModeNotifier = ref.read(viewModeStateProvider.notifier);
    final isCalendarView = viewMode == ViewMode.calendar;

    // Watch filter state from the existing provider
    final filterNotifier = ref.read(filterStateProvider.notifier);

    // Watch events using the simplified provider - ONE LINE!
    final eventsAsync = ref.watch(eventsForCurrentStateProvider);

    return Container(
      color: theme.scaffoldBackgroundColor,
      child: Column(
        children: [
          // Toggle Bar with dynamic highlighting based on selected view
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Stack(
              children: [
                // Add some padding to the Row to create space for the star icon
                Padding(
                  padding: const EdgeInsets.only(top: 10.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      IconButton(
                        icon: Icon(
                          Icons.calendar_month,
                          size: 30,
                          color: isCalendarView
                              ? Color(0xFF22D400)
                              : (theme.brightness == Brightness.dark
                                  ? Colors.white60
                                  : Colors.black45),
                        ),
                        onPressed: viewModeNotifier.setCalendarView,
                      ),
                      SizedBox(width: 30),
                      IconButton(
                        icon: Icon(
                          Icons.view_list,
                          size: 30,
                          color: !isCalendarView
                              ? Color(0xFF22D400)
                              : (theme.brightness == Brightness.dark
                                  ? Colors.white60
                                  : Colors.black45),
                        ),
                        onPressed: viewModeNotifier.setListView,
                      ),
                    ],
                  ),
                ),
                // Star Icon on the top right corner
              ],
            ),
          ),

          // Custom divider with dynamic highlighting
          Stack(
            children: [
              Divider(
                height: 1,
                thickness: 0.3,
                color: Colors.grey,
                indent: 0,
                endIndent: 0,
              ),
              Positioned(
                top: 0,
                left: 0,
                right: MediaQuery.of(context).size.width / 2,
                child: Container(
                  height: 1,
                  color:
                      isCalendarView ? Color(0xFF22D400) : Colors.transparent,
                ),
              ),
              Positioned(
                top: 0,
                left: MediaQuery.of(context).size.width / 2,
                right: 0,
                child: Container(
                  height: 1,
                  color:
                      !isCalendarView ? Color(0xFF22D400) : Colors.transparent,
                ),
              ),
            ],
          ),

          // Calendar and background image container
          Container(
            width: double.infinity,
            height: isCalendarView
                ? MediaQuery.of(context).size.height * 0.37
                : MediaQuery.of(context).size.height * 0.20,
            margin: EdgeInsets.symmetric(horizontal: 0),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Stack(
              children: [
                // Background image
                Container(
                  width: MediaQuery.of(context).size.width,
                  height: 200, // Fixed height for the banner
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: AssetImage('assets/images/settings_image.png'),
                      fit: BoxFit.cover,
                      colorFilter: ColorFilter.mode(
                        isDarkMode
                            ? Colors.white.withValues(alpha: 0.3)
                            : Color(0x3322D400),
                        BlendMode.darken,
                      ),
                    ),
                  ),
                ),

                // Calendar widget overlaid on top of the image when in calendar view
                if (isCalendarView)
                  Positioned.fill(
                    top: 10,
                    child: Center(
                      child: CalendarWidget(),
                    ),
                  )
                // Search field when not in calendar view (you can remove this if not using search)
                else
                  Positioned.fill(
                    child: Align(
                      alignment: Alignment(0.0, -0.3),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20.0),
                        child: Container(
                          height: 50,
                          decoration: BoxDecoration(
                            color:
                                isDarkMode ? Color(0xFF333333) : Colors.white,
                            borderRadius: BorderRadius.circular(25),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 4,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: TextField(
                            controller: _searchController,
                            style: TextStyle(
                              color:
                                  isDarkMode ? Colors.white : Color(0xFF626262),
                            ),
                            decoration: InputDecoration(
                              hintText: S.of(context).search,
                              hintStyle: TextStyle(
                                color: isDarkMode
                                    ? Colors.white60
                                    : Colors.black45,
                              ),
                              prefixIcon: Icon(
                                Icons.search,
                                color: isDarkMode
                                    ? Colors.white60
                                    : Colors.black45,
                              ),
                              border: InputBorder.none,
                              contentPadding: EdgeInsets.symmetric(
                                  vertical: 15, horizontal: 15),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),

                // Filter tags overlaid at the bottom of the image
                if (!isCalendarView)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        physics: BouncingScrollPhysics(),
                        padding: EdgeInsets.only(bottom: 10),
                        child: Row(
                          children: [
                            _buildFilterTag(S.of(context).all, FilterType.all,
                                filterNotifier),
                            SizedBox(width: 10),
                            _buildFilterTag(S.of(context).sportEvents,
                                FilterType.sportEvents, filterNotifier),
                            SizedBox(width: 10),
                            _buildFilterTag(S.of(context).cultureEvents,
                                FilterType.cultureEvents, filterNotifier),
                            SizedBox(width: 10),
                            _buildFilterTag(S.of(context).celebrationEvents,
                                FilterType.celebrationEvents, filterNotifier),
                          ],
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Events List with real data
          Expanded(
            child: eventsAsync.when(
              data: (events) {
                if (events.isEmpty) {
                  return Center(
                    child: Text(
                      'No events found',
                      style: TextStyle(
                        color: isDarkMode ? Colors.white : Color(0xFF626262),
                        fontSize: 16,
                      ),
                    ),
                  );
                }

                return ListView.builder(
                  padding: EdgeInsets.only(top: 10),
                  itemCount: events.length,
                  itemBuilder: (context, index) {
                    final event = events[index];
                    final title = event.name;
                    final startDate = event.startDate;

                    // Format the date for display
                    String formattedDate = 'No date';
                    if (startDate != null) {
                      formattedDate =
                          DateFormat('dd.MM.yyyy г.').format(startDate);
                    }

                    return Container(
                      margin: EdgeInsets.only(bottom: 16),
                      padding: EdgeInsets.only(top: 14),
                      child: GestureDetector(
                        onTap: () {
                          context.push('/events/event-details/${event.id}');
                        },
                        child: Container(
                          margin: EdgeInsets.only(right: 17),
                          width: MediaQuery.of(context).size.width * 0.9,
                          height: 90,
                          decoration: BoxDecoration(
                            color:
                                isDarkMode ? Color(0xFF333333) : Colors.white,
                            borderRadius: BorderRadius.only(
                              topRight: Radius.circular(10),
                              bottomRight: Radius.circular(10),
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: isDarkMode
                                    ? Colors.black.withValues(alpha: 0.2)
                                    : Colors.black.withValues(alpha: 0.1),
                                offset: Offset(4, 4),
                                blurRadius: 6,
                              ),
                            ],
                          ),
                          child: Stack(
                            clipBehavior: Clip.none,
                            children: [
                              // ListTile content
                              ListTile(
                                title: Padding(
                                  padding: const EdgeInsets.only(top: 14.0),
                                  child: Text(
                                    title,
                                    style: GoogleFonts.roboto(
                                      fontWeight: FontWeight.w400,
                                      fontSize: 14.9,
                                      height: 15 / 14,
                                      letterSpacing: 0.0,
                                      color: isDarkMode
                                          ? Colors.white
                                          : Color(0xFF626262),
                                    ),
                                  ),
                                ),
                              ),

                              // Date container at the top-right of the list item
                              Positioned(
                                top: -14,
                                right: 10,
                                child: Container(
                                  width: 101.136,
                                  height: 29,
                                  decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.only(
                                      topRight: Radius.circular(50),
                                      bottomLeft: Radius.circular(50),
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withOpacity(0.25),
                                        offset: Offset(0, 0),
                                        blurRadius: 10,
                                      ),
                                    ],
                                  ),
                                  child: Center(
                                    child: Text(
                                      formattedDate,
                                      style: GoogleFonts.roboto(
                                        fontWeight: FontWeight.w700,
                                        fontSize: 12.5,
                                        color: Colors.black,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
              loading: () => Center(
                child: CircularProgressIndicator(
                  color: Color(0xFF22D400),
                ),
              ),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 48,
                      color: isDarkMode ? Colors.white60 : Colors.black45,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Error loading events',
                      style: TextStyle(
                        color: isDarkMode ? Colors.white : Color(0xFF626262),
                        fontSize: 16,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      error.toString(),
                      style: TextStyle(
                        color: isDarkMode ? Colors.white60 : Colors.black45,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildFilterTag(
      String text, FilterType filterType, FilterState filterNotifier) {
    final isSelected = filterNotifier.isSelected(filterType);
    final isDarkMode = ref.watch(themeNotifierProvider) == ThemeMode.dark;

    return GestureDetector(
      onTap: () => filterNotifier.setFilter(filterType),
      child: Container(
        constraints: BoxConstraints(minWidth: 85),
        height: 32,
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 3),
        decoration: BoxDecoration(
          color: isSelected
              ? Color(0xFF22D400)
              : (isDarkMode ? Color(0xFF333333) : Colors.white),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            text,
            textAlign: TextAlign.center,
            style: TextStyle(
              fontFamily: 'Roboto',
              fontWeight: FontWeight.w500,
              fontSize: 14,
              letterSpacing: 0.25,
              color: isSelected
                  ? Color(0xDE000000)
                  : (isDarkMode ? Colors.white : Color(0xDE000000)),
            ),
          ),
        ),
      ),
    );
  }
}
