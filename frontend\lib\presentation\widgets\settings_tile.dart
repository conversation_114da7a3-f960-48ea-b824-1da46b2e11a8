import 'package:flutter/material.dart';

class SettingsTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData icon;
  final VoidCallback? onTap;
  final Widget? trailing;

  const SettingsTile({
    Key? key,
    required this.title,
    this.subtitle,
    required this.icon,
    this.onTap,
    this.trailing,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        leading: Icon(icon, color: theme.primaryColor),
        title: Text(title,
            style: TextStyle(
                fontSize: 16, color: theme.textTheme.bodyLarge?.color)),
        subtitle: subtitle != null
            ? Text(subtitle!,
                style: TextStyle(color: theme.textTheme.bodySmall?.color))
            : null,
        trailing:
            trailing ?? const Icon(Icons.chevron_right, color: Colors.black45),
        onTap: onTap,
      ),
    );
  }
}
