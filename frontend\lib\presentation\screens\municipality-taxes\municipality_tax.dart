import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:url_launcher/url_launcher.dart';

class MunicipalityTax extends ConsumerWidget {
  const MunicipalityTax({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Header Section with Background Image
              Container(
                width: double.infinity,
                height:
                    MediaQuery.of(context).orientation == Orientation.landscape
                        ? 150.0 // Fixed smaller height for landscape
                        : MediaQuery.of(context).size.height * 0.25,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('assets/images/settings_image.png'),
                    fit: BoxFit.cover,
                    colorFilter: ColorFilter.mode(
                      isDarkMode
                          ? Colors.white.withValues(alpha: 0.3)
                          : Color(0x3322D400),
                      BlendMode.darken,
                    ),
                  ),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.3),
                      ],
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.account_balance,
                        size: MediaQuery.of(context).orientation ==
                                Orientation.landscape
                            ? 36
                            : 48,
                        color: Colors.white,
                      ),
                      SizedBox(
                          height: MediaQuery.of(context).orientation ==
                                  Orientation.landscape
                              ? 6
                              : 8),
                      Text(
                        S.of(context).municipalityTaxes,
                        style: GoogleFonts.roboto(
                          fontSize: MediaQuery.of(context).orientation ==
                                  Orientation.landscape
                              ? 20
                              : 24,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Content Section
              Padding(
                padding:
                    MediaQuery.of(context).orientation == Orientation.landscape
                        ? const EdgeInsets.all(
                            16.0) // Reduced padding for landscape
                        : const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Information Card
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: isDarkMode ? Color(0xFF333333) : Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 6,
                            offset: Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.info_outline,
                                color: Color(0xFF22D400),
                                size: 24,
                              ),
                              SizedBox(width: 8),
                              Text(
                                S.of(context).information,
                                style: GoogleFonts.roboto(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: isDarkMode
                                      ? Colors.white
                                      : Color(0xFF626262),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 12),
                          Text(
                            S.of(context).chooseWayToPay,
                            style: GoogleFonts.roboto(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              height: 1.4,
                              color: isDarkMode
                                  ? Colors.white70
                                  : Color(0xFF626262),
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(
                        height: MediaQuery.of(context).orientation ==
                                Orientation.landscape
                            ? 16
                            : 24),

                    // Payment Options Title
                    Text(
                      S.of(context).waysToPay,
                      style: GoogleFonts.roboto(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: isDarkMode ? Colors.white : Color(0xFF626262),
                      ),
                    ),

                    SizedBox(height: 16),

                    // Payment Option 1 - КИН
                    _buildPaymentCard(
                      context: context,
                      isDarkMode: isDarkMode,
                      icon: Icons.credit_card,
                      title: S.of(context).kinNumber,
                      subtitle: S.of(context).kinDesc,
                      onTap: () async {
                        // TODO: Replace with actual КИН payment URL
                        final url =
                            Uri.parse('https://share.google/OhB9yDaA8FlE0jqID');
                        if (await canLaunchUrl(url)) {
                          await launchUrl(url,
                              mode: LaunchMode.externalApplication);
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text(S.of(context).problemKin)),
                          );
                        }
                      },
                    ),

                    SizedBox(
                        height: MediaQuery.of(context).orientation ==
                                Orientation.landscape
                            ? 12
                            : 16),

                    // Payment Option 2 - Electronic Signature
                    _buildPaymentCard(
                      context: context,
                      isDarkMode: isDarkMode,
                      icon: Icons.verified_user,
                      title: S.of(context).electronicSignature,
                      subtitle: S.of(context).electronicSignatureDesc,
                      onTap: () async {
                        // TODO: Replace with actual electronic signature payment URL
                        final url = Uri.parse(
                            'https://yambolidentity.imeon.bg:4443/Account/LoginCertificate?returnUrl=%2Fconnect%2Fauthorize%2Fcallback%3Fclient_id%3DImeon.Web.Public%26redirect_uri%3Dhttps%253A%252F%252Fyambol.imeon.bg%253A4443%252F%26response_mode%3Dform_post%26response_type%3Did_token%2520code%26scope%3Dopenid%2520Imeon.Web.Profile.Public%2520offline_access%26state%3DOpenIdConnect.AuthenticationProperties%253DML04QoR-pKlR6N5yR1QFLlG9n3IrouAjEVtMF2ksHL9x-CbIDlUdaYB0nq41FWOQk2mi1cjyPahSJ9bnUVsZhvhbs4ZZxJ5MAKygPU3fgB-dbUUYVd5zA2Mo-34Wh0IsYBbJZFmmBE1qoEcFN_LBHhI6ks_jvzIgoDjGtfsZ2g7OwFA5V2Xgdv8eaoVhsOHiER2V5P-94V_tex9vp7dc_UvJUOhP1lcdx7jV8-1MXdth3CL4UbpaoZhDdmtyrhkc%26nonce%3D638853224697511629.OTBhMWU3ZWUtODY4NS00NmFlLWJlZDYtMDNhNmQ3YTU3MDE0NzE5MTA0ZWQtNGJlZS00OWQ0LTkwYTctY2IyYWIwY2IxMDhk%26x-client-SKU%3DID_NET451%26x-client-ver%3D5.2.1.0');
                        if (await canLaunchUrl(url)) {
                          await launchUrl(url,
                              mode: LaunchMode.externalApplication);
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                                content: Text(
                                    S.of(context).problemElectronicSignature)),
                          );
                        }
                      },
                    ),

                    SizedBox(
                        height: MediaQuery.of(context).orientation ==
                                Orientation.landscape
                            ? 16
                            : 24),

                    // Help Section
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Color(0xFF22D400).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Color(0xFF22D400).withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.help_outline,
                            color: Color(0xFF22D400),
                            size: 20,
                          ),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              S.of(context).userHelp,
                              style: GoogleFonts.roboto(
                                fontSize: 13,
                                fontWeight: FontWeight.w400,
                                color: isDarkMode
                                    ? Colors.white
                                    : Color(0xFF626262),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentCard({
    required BuildContext context,
    required bool isDarkMode,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isDarkMode ? Color(0xFF333333) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 6,
              offset: Offset(0, 3),
            ),
          ],
          border: Border.all(
            color: Color(0xFF22D400).withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: Color(0xFF22D400).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(28),
              ),
              child: Icon(
                icon,
                color: Color(0xFF22D400),
                size: 28,
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: GoogleFonts.roboto(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isDarkMode ? Colors.white : Color(0xFF626262),
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: GoogleFonts.roboto(
                      fontSize: 13,
                      fontWeight: FontWeight.w400,
                      color: isDarkMode ? Colors.white70 : Color(0xFF888888),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: Color(0xFF22D400),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
