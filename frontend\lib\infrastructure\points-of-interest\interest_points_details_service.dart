import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:frontend/models/get_interest_points_by_id_command.dart';

class InterestPointDetailsService {
  static final Dio _dio = Dio(BaseOptions(
    baseUrl: dotenv.env['BASE_URL']!, // Load BASE_URL from .env
  ));

  /// **Fetch interest point details**
  static Future<InterestPointDetailsModel> getInterestPointDetails(
      int id, String? languageCode) async {
    final String endpoint =
        'InterestPoints/$id/Information'; // Adjust if needed

    // Build query parameters dynamically based on the inputs
    final Map<String, dynamic> queryParams = {};

    if (languageCode != null && languageCode.isNotEmpty) {
      queryParams['languageCode'] = languageCode;
    }

    try {
      final response = await _dio.get(endpoint, queryParameters: queryParams);

      print(response.data);

      if (response.statusCode == 200) {
        return InterestPointDetailsModel.fromJson(response.data);
      } else {
        throw Exception(
            'Failed to load details with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      throw Exception('Failed to fetch details');
    } catch (e) {
      print("⚠ Unexpected Error: $e");
      throw Exception('Something went wrong');
    }
  }
}
