name: frontend
description: "A new Flutter project."

# Prevent accidental publication to pub.dev
publish_to: 'none' 

# Application version
version: 1.0.0+1

# Flutter and Dart SDK version
environment:
  sdk: ^3.6.1

# 🔹 Dependencies required for the app to function
dependencies:
  flutter:
    sdk: flutter
  flutter_riverpod: ^2.6.1       # State management
  riverpod_annotation: ^2.6.1    # Required for `g.dart`
  dio: ^5.4.0                    # Networking
  go_router: ^14.8.0             # Navigation
  shared_preferences: ^2.2.2     # Persistent storage for language settings
  flutter_localizations:         # Localization support (l10n)
    sdk: flutter
  intl: ^0.19.0                  # Match Flutter's `flutter_localizations`
  google_fonts: 6.2.1
  curved_navigation_bar: ^1.0.6
  persistent_bottom_nav_bar: ^6.2.1
  google_maps_flutter: ^2.11.0
  flutter_image_compress: ^2.4.0
  device_preview: ^1.2.0
  weather_icons: ^3.0.0
  flutter_svg: ^2.0.17
  carousel_slider: ^5.0.0
  geolocator: ^10.1.0
  

  # 🔹 Cupertino Icons for iOS-style icons
  cupertino_icons: ^1.0.8
  flutter_dotenv: ^5.2.1
  flutter_secure_storage: ^9.0.0
  shimmer: ^3.0.0
  app_links: ^6.4.0
  url_launcher: ^6.3.1
  flutter_html: ^3.0.0
  html: ^0.15.6

# 🔹 Developer dependencies for code generation
dev_dependencies:
  flutter_test:
    sdk: flutter
  riverpod_generator: ^2.6.1     # Generates `g.dart` files
  build_runner: ^2.4.6           # Required for running `flutter pub run build_runner build`
  flutter_lints: ^5.0.0
  intl_translation: any           # For localization code generation

# 🔹 Flutter-specific settings
flutter:
  generate: true  # Enables Flutter's localization (l10n) generation

  # 🔹 Assets section for images and other resources
  assets:
    - assets/images/
    - assets/icons/
    - .env.staging
    - .env.local
    - assets/map_styles/dark_map.json
    - assets/map_styles/light_map.json

  # 🔹 Ensures Material Icons are included
  uses-material-design: true
