// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'events_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$eventsForCurrentStateHash() =>
    r'032eca698e39942cce354c7248844b83c1f91a5c';

/// See also [eventsForCurrentState].
@ProviderFor(eventsForCurrentState)
final eventsForCurrentStateProvider =
    AutoDisposeFutureProvider<List<EventsGetModel>>.internal(
  eventsForCurrentState,
  name: r'eventsForCurrentStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$eventsForCurrentStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef EventsForCurrentStateRef
    = AutoDisposeFutureProviderRef<List<EventsGetModel>>;
String _$fetchEventByIdHash() => r'1c72492e9d5bdc7a51a2ed022c99457ee54f034a';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [fetchEventById].
@ProviderFor(fetchEventById)
const fetchEventByIdProvider = FetchEventByIdFamily();

/// See also [fetchEventById].
class FetchEventByIdFamily extends Family<AsyncValue<EventsDetailsModel?>> {
  /// See also [fetchEventById].
  const FetchEventByIdFamily();

  /// See also [fetchEventById].
  FetchEventByIdProvider call(
    int eventId,
  ) {
    return FetchEventByIdProvider(
      eventId,
    );
  }

  @override
  FetchEventByIdProvider getProviderOverride(
    covariant FetchEventByIdProvider provider,
  ) {
    return call(
      provider.eventId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'fetchEventByIdProvider';
}

/// See also [fetchEventById].
class FetchEventByIdProvider
    extends AutoDisposeFutureProvider<EventsDetailsModel?> {
  /// See also [fetchEventById].
  FetchEventByIdProvider(
    int eventId,
  ) : this._internal(
          (ref) => fetchEventById(
            ref as FetchEventByIdRef,
            eventId,
          ),
          from: fetchEventByIdProvider,
          name: r'fetchEventByIdProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$fetchEventByIdHash,
          dependencies: FetchEventByIdFamily._dependencies,
          allTransitiveDependencies:
              FetchEventByIdFamily._allTransitiveDependencies,
          eventId: eventId,
        );

  FetchEventByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.eventId,
  }) : super.internal();

  final int eventId;

  @override
  Override overrideWith(
    FutureOr<EventsDetailsModel?> Function(FetchEventByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: FetchEventByIdProvider._internal(
        (ref) => create(ref as FetchEventByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        eventId: eventId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<EventsDetailsModel?> createElement() {
    return _FetchEventByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is FetchEventByIdProvider && other.eventId == eventId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, eventId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin FetchEventByIdRef on AutoDisposeFutureProviderRef<EventsDetailsModel?> {
  /// The parameter `eventId` of this provider.
  int get eventId;
}

class _FetchEventByIdProviderElement
    extends AutoDisposeFutureProviderElement<EventsDetailsModel?>
    with FetchEventByIdRef {
  _FetchEventByIdProviderElement(super.provider);

  @override
  int get eventId => (origin as FetchEventByIdProvider).eventId;
}

String _$searchStateHash() => r'f807cb03255ff51036d7d2241ef17503aa80233b';

/// See also [SearchState].
@ProviderFor(SearchState)
final searchStateProvider =
    AutoDisposeNotifierProvider<SearchState, String>.internal(
  SearchState.new,
  name: r'searchStateProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$searchStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SearchState = AutoDisposeNotifier<String>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
