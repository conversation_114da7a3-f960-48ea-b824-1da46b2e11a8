import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/settings_header.dart';
import '../widgets/settings_options.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          children: const [
            SettingsHeader(), // ✅ Profile section
            SizedBox(height: 20),
            SettingsOptions(), // ✅ Settings options section
          ],
        ),
      ),
    );
  }
}
