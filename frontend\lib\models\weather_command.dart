import 'dart:convert';

class Weather {
  final double temperature;
  final double windSpeed;
  final double humidity;
  final int weatherCode;
  final List<double> hourlyTemperatures;
  final List<int> hourlyWeatherCode;
  final List<int> dailyWeatherCode;
  final List<double> dailyTempMin;
  final List<double> dailyTempMax;

  Weather({
    required this.temperature,
    required this.windSpeed,
    required this.humidity,
    required this.weatherCode,
    required this.hourlyTemperatures,
    required this.hourlyWeatherCode,
    required this.dailyWeatherCode,
    required this.dailyTempMin,
    required this.dailyTempMax,
  });

  factory Weather.fromJson(Map<String, dynamic> json) {
    return Weather(
      temperature: json['current']['temperature_2m']?.toDouble() ?? 0.0,
      windSpeed: json['current']['wind_speed_10m']?.toDouble() ?? 0.0,
      humidity: json['current']['relative_humidity_2m']?.toDouble() ?? 0.0,
      weatherCode: json['current']['weather_code']?.toInt() ?? 0,
      hourlyTemperatures: (json['hourly']['temperature_2m'] as List<dynamic>)
          .map((temp) => temp != null ? (temp as num).toDouble() : 0.0)
          .toList(),
      hourlyWeatherCode: (json['hourly']['weather_code'] as List<dynamic>)
          .map((code) => code != null ? (code as num).toInt() : 0)
          .toList(),
      dailyWeatherCode: (json['daily']['weather_code'] as List<dynamic>)
          .map((code) => code != null ? (code as num).toInt() : 0)
          .toList(),
      dailyTempMin: (json['daily']['temperature_2m_min'] as List<dynamic>)
          .map((temp) => temp != null ? (temp as num).toDouble() : 0.0)
          .toList(),
      dailyTempMax: (json['daily']['temperature_2m_max'] as List<dynamic>)
          .map((temp) => temp != null ? (temp as num).toDouble() : 0.0)
          .toList(),
    );
  }

  static Weather fromRawJson(String str) => Weather.fromJson(json.decode(str));
}
