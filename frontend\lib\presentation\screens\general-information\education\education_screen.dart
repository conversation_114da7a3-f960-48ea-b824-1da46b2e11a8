import 'package:flutter/material.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class EducationScreen extends ConsumerWidget {
  const EducationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final themeMode =
        ref.watch(themeNotifierProvider); // Access themeMode from provider

    final List<String> educationMenuItems = [
      S.of(context).kindergardens,
      S.of(context).nursery,
      S.of(context).childNutritionCenter,
      S.of(context).schools,
      S.of(context).universities,
      S.of(context).developmentCenters
    ];

    // Determine if it's dark mode
    final isDarkMode = themeMode == ThemeMode.dark;

    return Scaffold(
      backgroundColor:
          isDarkMode ? Colors.black : Colors.white, // Dark mode background
      body: Padding(
        padding: const EdgeInsets.all(0),
        child: Stack(
          children: [
            // Top Banner Image
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Stack(
                  children: [
                    Container(
                      width: MediaQuery.of(context).size.width,
                      height: 200, // Fixed height for the banner
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: AssetImage('assets/images/general-info.png'),
                          fit: BoxFit.cover,
                          colorFilter: ColorFilter.mode(
                            isDarkMode
                                ? Colors.white.withValues(alpha: 0.3)
                                : Color(0x0F22D400), // Lighter dark mode filter
                            BlendMode.darken,
                          ),
                        ),
                      ),
                    ),
                    Positioned.fill(
                      child: Align(
                        alignment: Alignment.center,
                        child: Padding(
                          padding: const EdgeInsets.only(
                              top: 0), // Adjust top padding as needed
                          child: Text(
                            S.of(context).educationYambolWelcomeMessage,
                            textAlign: TextAlign.center,
                            style: GoogleFonts.roboto(
                              fontWeight: FontWeight.w700,
                              fontSize: 17,
                              height: 15 / 14,
                              letterSpacing: 0.0,
                              color: isDarkMode
                                  ? Colors.white
                                  : Color(
                                      0xFF626262), // White text in dark mode
                            ),
                          ),
                        ),
                      ),
                    ),
                    Positioned(
                      top: 16,
                      right: 16,
                      child: Icon(
                        Icons.star,
                        color: isDarkMode
                            ? Colors.white
                            : Color(
                                0xFF626262), // Icon color for dark/light mode
                        size: 30,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            // Static Menu List
            Positioned(
              top: 165, // Fixed position for the menu list
              left: 0,
              right: 13,
              bottom: 0,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 0),
                child: ListView.builder(
                  itemCount: educationMenuItems.length,
                  itemBuilder: (context, index) {
                    final item = educationMenuItems[index];

                    return GestureDetector(
                      onTap: () {
                        if (item == S.of(context).kindergardens) {
                          context.push('/general/education/kindergardens');
                        } else if (item == S.of(context).nursery) {
                          context.push('/general/education/nurseries');
                        } else if (item == S.of(context).childNutritionCenter) {
                          context
                              .push('/general/education/childNutritionCenters');
                        } else if (item == S.of(context).schools) {
                          context.push('/general/education/schools');
                        } else if (item == S.of(context).universities) {
                          context.push('/general/education/universities');
                        } else if (item == S.of(context).developmentCenters) {
                          context.push('/general/education/developmentCenters');
                        }
                      },
                      child: Container(
                        margin: EdgeInsets.only(bottom: 16),
                        width: 400, // Fixed width for menu items
                        height: 76, // Fixed height for menu items
                        decoration: BoxDecoration(
                          color: isDarkMode
                              ? Color(0xFF333333)
                              : Colors
                                  .white, // Dark background for items in dark mode
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(10),
                            bottomRight: Radius.circular(10),
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: isDarkMode
                                  ? Colors.black.withValues(alpha: 0.2)
                                  : Colors.black.withValues(alpha: 0.1),
                              offset: Offset(4, 4),
                              blurRadius: 6,
                            ),
                          ],
                        ),
                        child: ListTile(
                          leading: Padding(
                            padding: const EdgeInsets.only(top: 8.0, left: 3.5),
                            child: Icon(
                              Icons.star,
                              color: isDarkMode
                                  ? Colors.white
                                  : Color(
                                      0xFFBABBBA), // Icon color change based on theme
                            ),
                          ),
                          title: Padding(
                            padding: const EdgeInsets.only(top: 14.0),
                            child: Text(
                              item,
                              style: GoogleFonts.roboto(
                                fontWeight: FontWeight.w400,
                                fontSize: 17,
                                height: 15 / 14,
                                letterSpacing: 0.0,
                                color: isDarkMode
                                    ? Colors.white
                                    : Color(
                                        0xFF626262), // Text color change based on theme
                              ),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
