import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:go_router/go_router.dart';
import '../widgets/custom_bottom_nav.dart';
import '../widgets/custom_app_bar.dart';

class MainScreen extends ConsumerWidget {
  final Widget child; // This will be passed by GoRouter's ShellRoute

  const MainScreen({super.key, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final routerState = GoRouterState.of(context); // Get current route
    String appBarTitle = "Smart Yambol"; // Default AppBar title

    // Extract path parameters
    final pathParameters = routerState.pathParameters;

    // Check if the route has an ID (dynamic route)
    if (pathParameters.containsKey('id')) {
      //final path = routerState.uri.path;
      final pathSegments = routerState.uri.pathSegments;

// Determine if it's a dynamic route with 4 or 3 segments
      if (pathSegments.length >= 3) {
        String? category;
        String? subPath;
        String? point;

        if (pathSegments.length == 4) {
          // Route format: /category/subPath/point/id
          category = pathSegments[0];
          subPath = pathSegments[1];
          point = pathSegments[2];
        } else if (pathSegments.length == 3) {
          // Route format: /category/point/id
          category = pathSegments[0];
          point = pathSegments[1];
        }

        if (category == 'tourism' && point == 'landmarks') {
          appBarTitle = S.of(context).tourismSights;
        } else if (category == 'tourism' && point == 'culture') {
          appBarTitle = S.of(context).tourismCulturalSites;
        } else if (category == 'tourism' && point == 'hiking') {
          appBarTitle = S.of(context).tourismRoutesAndActivities;
        } else if (category == 'tourism' && point == 'family') {
          appBarTitle = S.of(context).tourismFamilyEntertainment;
        } else if (category == 'tourism' && point == 'nightLife') {
          appBarTitle = S.of(context).tourismNightlife;
        } else if (category == 'tourism' && point == 'transport') {
          appBarTitle = S.of(context).tourismTransport;
        } else if (category == 'tourism' && point == 'toruistAgency') {
          appBarTitle = S.of(context).tourismTravelAgencies;
        } else if (category == 'tourism' && point == 'legendsAndMyths') {
          appBarTitle = S.of(context).tourismLegendsAndMyths;
        }

        if (category == 'general' && point == 'gasStation') {
          appBarTitle = S.of(context).gasStations;
        } else if (category == 'general' && point == 'shop') {
          appBarTitle = S.of(context).shop;
        } else if (category == 'general' &&
            subPath == 'establishments' &&
            point == 'restaurant') {
          appBarTitle = S.of(context).restaurants;
        } else if (category == 'general' &&
            subPath == 'establishments' &&
            point == 'coffee') {
          appBarTitle = S.of(context).coffee;
        } else if (category == 'general' &&
            subPath == 'establishments' &&
            point == 'bar') {
          appBarTitle = S.of(context).bars;
        } else if (category == 'general' &&
            subPath == 'establishments' &&
            point == 'pastryShop') {
          appBarTitle = S.of(context).pastryShops;
        } else if (category == 'general' &&
            subPath == 'accommodations' &&
            point == 'hotel') {
          appBarTitle = S.of(context).hotels;
        } else if (category == 'general' &&
            subPath == 'accommodations' &&
            point == 'guestHouse') {
          appBarTitle = S.of(context).guestHouses;
        } else if (category == 'general' &&
            subPath == 'finance' &&
            point == 'bank') {
          appBarTitle = S.of(context).banks;
        } else if (category == 'general' &&
            subPath == 'finance' &&
            point == 'currencyExchange') {
          appBarTitle = S.of(context).currencyExchanges;
        } else if (category == 'general' &&
            subPath == 'finance' &&
            point == 'insuranceCompany') {
          appBarTitle = S.of(context).insuranceCompanies;
        } else if (category == 'general' &&
            subPath == 'finance' &&
            point == 'atm') {
          appBarTitle = S.of(context).atms;
        } else if (category == 'general' &&
            subPath == 'ecology' &&
            point == 'bioShop') {
          appBarTitle = S.of(context).bioShops;
        } else if (category == 'general' &&
            subPath == 'ecology' &&
            point == 'farm') {
          appBarTitle = S.of(context).farms;
        } else if (category == 'general' &&
            subPath == 'ecology' &&
            point == 'recycling') {
          appBarTitle = S.of(context).recycling;
        } else if (category == 'general' &&
            subPath == 'ecology' &&
            point == 'ecoInitiative') {
          appBarTitle = S.of(context).ecoInitiatives;
        } else if (category == 'general' &&
            subPath == 'culture' &&
            point == 'museum') {
          appBarTitle = S.of(context).museums;
        } else if (category == 'general' &&
            subPath == 'culture' &&
            point == 'theater') {
          appBarTitle = S.of(context).theaters;
        } else if (category == 'general' &&
            subPath == 'culture' &&
            point == 'gallery') {
          appBarTitle = S.of(context).galleries;
        } else if (category == 'general' &&
            subPath == 'education' &&
            point == 'kindergarden') {
          appBarTitle = S.of(context).kindergardens;
        } else if (category == 'general' &&
            subPath == 'education' &&
            point == 'nursery') {
          appBarTitle = S.of(context).nursery;
        } else if (category == 'general' &&
            subPath == 'education' &&
            point == 'childNutritionCenter') {
          appBarTitle = S.of(context).childNutritionCenter;
        } else if (category == 'general' &&
            subPath == 'education' &&
            point == 'school') {
          appBarTitle = S.of(context).schools;
        } else if (category == 'general' &&
            subPath == 'education' &&
            point == 'university') {
          appBarTitle = S.of(context).universities;
        } else if (category == 'general' &&
            subPath == 'education' &&
            point == 'developmentCenter') {
          appBarTitle = S.of(context).developmentCenters;
        } else if (category == 'general' &&
            subPath == 'health' &&
            point == 'pharmacy') {
          appBarTitle = S.of(context).pharmacies;
        } else if (category == 'general' &&
            subPath == 'health' &&
            point == 'medicalEstablishment') {
          appBarTitle = S.of(context).medicalEstablishments;
        } else if (category == 'general' &&
            subPath == 'health' &&
            point == 'doctorsOffice') {
          appBarTitle = S.of(context).doctorsOffices;
        } else if (category == 'general' &&
            subPath == 'health' &&
            point == 'medicalLab') {
          appBarTitle = S.of(context).medicalLabs;
        } else if (category == 'general' &&
            subPath == 'health' &&
            point == 'veterinary') {
          appBarTitle = S.of(context).veterinaries;
        } else if (category == 'general' &&
            subPath == 'sport' &&
            point == 'sportFacility') {
          appBarTitle = S.of(context).sportFacilities;
        }
      }
    } else {
      // Handle static routes
      final currentPath = routerState.uri.path;

      if (currentPath == '/tourism') {
        appBarTitle = S.of(context).tourism;
      } else if (currentPath == '/favorites') {
        appBarTitle = S.of(context).favorites;
      } else if (currentPath == '/location') {
        appBarTitle = S.of(context).location;
      } else if (currentPath == '/settings') {
        appBarTitle = S.of(context).settings;
      } else if (currentPath == '/messages') {
        appBarTitle = S.of(context).messages;
      } else if (currentPath == '/tourism/legends-and-myths') {
        appBarTitle = S.of(context).tourismLegendsAndMyths;
      } else if (currentPath == '/tourism/tourism-map') {
        appBarTitle = S.of(context).map;
      } else if (currentPath == '/events') {
        appBarTitle = S.of(context).events;
      } else if (currentPath == '/tourism/landmarks') {
        appBarTitle = S.of(context).tourismSights;
      } else if (currentPath == '/tourism/cultural-and-artistic-sites') {
        appBarTitle = S.of(context).tourismCulturalSites;
      } else if (currentPath == '/tourism/routes-and-activities') {
        appBarTitle = S.of(context).tourismRoutesAndActivities;
      } else if (currentPath == '/tourism/family-entertainment') {
        appBarTitle = S.of(context).tourismFamilyEntertainment;
      } else if (currentPath == '/tourism/night-life') {
        appBarTitle = S.of(context).tourismNightlife;
      } else if (currentPath == '/tourism/transport') {
        appBarTitle = S.of(context).tourismTransport;
      } else if (currentPath == '/tourism/travel-agencies') {
        appBarTitle = S.of(context).tourismTravelAgencies;
      } else if (currentPath == '/general') {
        appBarTitle = S.of(context).generalInformationFull;
      } else if (currentPath == '/general/gas-stations') {
        appBarTitle = S.of(context).gasStations;
      } else if (currentPath == '/general/shops') {
        appBarTitle = S.of(context).shop;
      } else if (currentPath == '/general/establishments') {
        appBarTitle = S.of(context).establishments;
      } else if (currentPath == '/general/establishments/restaurants') {
        appBarTitle = S.of(context).restaurants;
      } else if (currentPath == '/general/establishments/coffes') {
        appBarTitle = S.of(context).coffee;
      } else if (currentPath == '/general/establishments/bars') {
        appBarTitle = S.of(context).bars;
      } else if (currentPath == '/general/establishments/pastryShops') {
        appBarTitle = S.of(context).pastryShops;
      } else if (currentPath == '/general/accommodations') {
        appBarTitle = S.of(context).accommodation;
      } else if (currentPath == '/general/accommodations/hotels') {
        appBarTitle = S.of(context).hotels;
      } else if (currentPath == '/general/accommodations/guestHouses') {
        appBarTitle = S.of(context).guestHouses;
      } else if (currentPath == '/general/finance') {
        appBarTitle = S.of(context).finance;
      } else if (currentPath == '/general/finance/banks') {
        appBarTitle = S.of(context).banks;
      } else if (currentPath == '/general/finance/currencyExchanges') {
        appBarTitle = S.of(context).currencyExchanges;
      } else if (currentPath == '/general/finance/insuranceCompanies') {
        appBarTitle = S.of(context).insuranceCompanies;
      } else if (currentPath == '/general/finance/atms') {
        appBarTitle = S.of(context).atms;
      } else if (currentPath == '/general/ecology') {
        appBarTitle = S.of(context).ecology;
      } else if (currentPath == '/general/ecology/bioShops') {
        appBarTitle = S.of(context).bioShops;
      } else if (currentPath == '/general/ecology/farms') {
        appBarTitle = S.of(context).farms;
      } else if (currentPath == '/general/ecology/recyclings') {
        appBarTitle = S.of(context).recycling;
      } else if (currentPath == '/general/ecology/ecoInitiatives') {
        appBarTitle = S.of(context).ecoInitiatives;
      } else if (currentPath == '/general/culture') {
        appBarTitle = S.of(context).culture;
      } else if (currentPath == '/general/culture/museums') {
        appBarTitle = S.of(context).museums;
      } else if (currentPath == '/general/culture/theaters') {
        appBarTitle = S.of(context).theaters;
      } else if (currentPath == '/general/culture/galleries') {
        appBarTitle = S.of(context).galleries;
      } else if (currentPath == '/general/education') {
        appBarTitle = S.of(context).education;
      } else if (currentPath == '/general/education/kindergardens') {
        appBarTitle = S.of(context).kindergardens;
      } else if (currentPath == '/general/education/nurseries') {
        appBarTitle = S.of(context).nursery;
      } else if (currentPath == '/general/education/childNutritionCenters') {
        appBarTitle = S.of(context).childNutritionCenter;
      } else if (currentPath == '/general/education/schools') {
        appBarTitle = S.of(context).schools;
      } else if (currentPath == '/general/education/universities') {
        appBarTitle = S.of(context).universities;
      } else if (currentPath == '/general/education/developmentCenters') {
        appBarTitle = S.of(context).developmentCenters;
      } else if (currentPath == '/general/health') {
        appBarTitle = S.of(context).health;
      } else if (currentPath == '/general/health/pharmacies') {
        appBarTitle = S.of(context).pharmacies;
      } else if (currentPath == '/general/health/medicalEstablishments') {
        appBarTitle = S.of(context).medicalEstablishments;
      } else if (currentPath == '/general/health/doctorsOffices') {
        appBarTitle = S.of(context).doctorsOffices;
      } else if (currentPath == '/general/health/medicalLabs') {
        appBarTitle = S.of(context).medicalLabs;
      } else if (currentPath == '/general/health/veterinaries') {
        appBarTitle = S.of(context).veterinaries;
      } else if (currentPath == '/general/sport') {
        appBarTitle = S.of(context).sport;
      } else if (currentPath == '/general/sport/sportFacilities') {
        appBarTitle = S.of(context).sportFacilities;
      } else if (currentPath == '/news') {
        appBarTitle = S.of(context).news;
      } else if (currentPath == '/taxes') {
        appBarTitle = S.of(context).taxes;
      }
    }

    return Scaffold(
      appBar: CustomAppBar(title: appBarTitle),
      body: child,
      bottomNavigationBar:
          const CustomBottomNavBar(), // Remove SafeArea wrapper
    );
  }
}
