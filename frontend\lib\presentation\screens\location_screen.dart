import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/presentation/widgets/reusable_map_screen.dart';

class LocationScreen extends ConsumerWidget {
  const LocationScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // These are the same filter groups from your original implementation
    const Map<String, List<String>> filterGroups = {
      'Business': [
        'Restaurant',
        'Coffee',
        'Bars',
        'PastryShop',
        'GasStation',
        'Shop',
        'Hotel',
        'GuestHouse',
        'Bank',
        'CurrencyChange',
        'InsuranceCompany',
        'Atm',
        'BioShop',
        'Farm',
        'RecycleCenter',
        'EcologyInitiative',
      ],
      'Landmarks': [
        'Landmark',
        'LegendsAndMyths',
        'Culture',
        'Hiking',
        'Family',
        'NightLife',
        'Transport',
        'TouristAgency',
        'Museum',
        'Theatre',
        'Gallery',
      ],
      'Healthcare': [
        'Pharmacy',
        'MedicalEstablishment',
        'DoctorsOffice',
        'MedicalLab',
        'Veterinary',
      ],
      'Education': [
        'Kindergarden',
        'Nursery',
        'ChildNutritionCenter',
        'School',
        'University',
        'DevelopmentCenter',
      ],
      'Sports': [
        'SportsFacility',
      ],
    };

    // Same initial selected filters as your original
    const Set<String> initialSelectedFilters = {
      'Business',
      'Landmarks',
      'Healthcare'
    };

    return Scaffold(
      body: ReusableMapWidget(
        filterGroups: filterGroups,
        initialSelectedFilters: initialSelectedFilters,
        showFilters: true, // Enable filters for location screen
        title: 'Location Map',
      ),
    );
  }
}
