import 'package:flutter/material.dart';
import 'package:frontend/infrastructure/weather/weather_icon_service.dart';

class HourlyForecastCard extends StatelessWidget {
  final double temperature;
  final int weatherCode;
  final WeatherIconService iconService;
  final String time;

  const HourlyForecastCard({
    required this.temperature,
    required this.weatherCode,
    required this.iconService,
    required this.time,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.3),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          Text(time, style: TextStyle(color: Colors.white, fontSize: 16)),
          Icon(iconService.showWeatherIcon(weatherCode),
              color: Colors.white, size: 30),
          Text('${temperature.toStringAsFixed(1)}°C',
              style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white)),
        ],
      ),
    );
  }
}
