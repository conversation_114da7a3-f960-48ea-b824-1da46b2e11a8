import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for Bulgarian (`bg`).
class SBg extends S {
  SBg([String locale = 'bg']) : super(locale);

  @override
  String get appTitle => 'Смарт Ямбол';

  @override
  String get guest => 'Гост';

  @override
  String get settings => 'Настройки';

  @override
  String get darkMode => 'Тъмна тема';

  @override
  String get language => 'Език';

  @override
  String get terms => 'Условия за използване';

  @override
  String get login => 'Вход';
}
