import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../l10n/generated/l10n.dart';
import '../widgets/register_form.dart';
import '../widgets/register_actions.dart';

class RegisterScreen extends ConsumerWidget {
  const RegisterScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _formKey = GlobalKey<FormState>(); // ✅ Create form key

    // ✅ Create controllers
    final TextEditingController emailController = TextEditingController();
    final TextEditingController passwordController = TextEditingController();
    final TextEditingController confirmPasswordController =
        TextEditingController();
    final TextEditingController firstNameController = TextEditingController();
    final TextEditingController lastNameController = TextEditingController();

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        title: Text(
          S.of(context).register,
          style: const TextStyle(
              color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: Stack(
        children: [
          // ✅ Background Image
          Positioned.fill(
            child: Opacity(
              opacity: 0.35, // ✅ Adjust this value to change transparency
              child: Transform.scale(
                scale: 1.75, // ✅ Adjust to zoom in
                child: Image.asset(
                  'assets/images/login_background.png', // ✅ Replace with actual image
                  fit: BoxFit.cover, // ✅ Ensures full coverage
                  alignment: Alignment
                      .center, // ✅ Keeps the center of the image visible
                ),
              ),
            ),
          ),

          // ✅ Main Content
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  const SizedBox(height: 60), // ✅ Offset for AppBar

                  // ✅ Pass controllers to RegisterForm
                  RegisterForm(
                    formKey: _formKey,
                    emailController: emailController,
                    passwordController: passwordController,
                    confirmPasswordController: confirmPasswordController,
                    firstNameController: firstNameController,
                    lastNameController: lastNameController,
                  ),

                  const SizedBox(height: 20),

                  // ✅ Pass controllers to RegisterActions
                  RegisterActions(
                    formKey: _formKey,
                    emailController: emailController,
                    passwordController: passwordController,
                    confirmPasswordController: confirmPasswordController,
                    firstNameController: firstNameController,
                    lastNameController: lastNameController,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
