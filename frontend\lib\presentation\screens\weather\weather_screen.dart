import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/infrastructure/weather/weather_icon_service.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/models/weather_command.dart';
import 'package:frontend/presentation/widgets/weather/hourly_forecast_card.dart';
import 'package:frontend/presentation/widgets/weather/weather_details_card.dart';
import 'package:frontend/presentation/widgets/weather/weather_forecast_card.dart';
import 'package:frontend/providers/weather_provider.dart';
import 'package:shimmer/shimmer.dart';
import 'package:weather_icons/weather_icons.dart';

class WeatherScreen extends ConsumerWidget {
  final WeatherIconService _iconService = WeatherIconService();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final weatherAsync = ref.watch(weatherProvider);

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          S.of(context).weatherTitle, // Translated title
          style: TextStyle(color: Colors.white),
        ),
        centerTitle: true,
        actions: [
          IconButton(
            icon: Icon(Icons.refresh, color: Colors.white),
            onPressed: () => ref.invalidate(weatherProvider),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.green.shade700, Colors.green.shade300],
          ),
        ),
        child: SafeArea(
          child: RefreshIndicator(
            onRefresh: () async {
              ref.invalidate(weatherProvider);
            },
            child: weatherAsync.when(
              data: (Weather weather) {
                return LayoutBuilder(
                  builder: (context, constraints) {
                    return SingleChildScrollView(
                      physics: AlwaysScrollableScrollPhysics(),
                      child: ConstrainedBox(
                        constraints: BoxConstraints(
                          minHeight: constraints.maxHeight,
                        ),
                        child: Padding(
                          padding: EdgeInsets.all(16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                S.of(context).cityName, // Translated city name
                                style: TextStyle(
                                    fontSize: 32,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white),
                              ),
                              Text(
                                '${weather.temperature.toStringAsFixed(1)}°C',
                                style: TextStyle(
                                    fontSize: 72,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white),
                              ),
                              Icon(
                                _iconService
                                    .showWeatherIcon(weather.weatherCode),
                                size: 80,
                                color: Colors.white,
                              ),
                              SizedBox(height: 20),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  WeatherDetailCard(
                                    icon: WeatherIcons.strong_wind,
                                    value:
                                        '${weather.windSpeed.toStringAsFixed(1)} km/h',
                                    label: S.of(context).wind, // Translated
                                  ),
                                  WeatherDetailCard(
                                    icon: WeatherIcons.humidity,
                                    value:
                                        '${weather.humidity.toStringAsFixed(1)}%',
                                    label: S.of(context).humidity, // Translated
                                  ),
                                ],
                              ),
                              SizedBox(height: 20),
                              SizedBox(
                                height: 140,
                                child: ListView.separated(
                                  scrollDirection: Axis.horizontal,
                                  padding: EdgeInsets.symmetric(horizontal: 16),
                                  itemCount: weather.hourlyTemperatures.length
                                      .clamp(0, 25),
                                  separatorBuilder: (context, index) =>
                                      SizedBox(width: 12),
                                  itemBuilder: (context, index) {
                                    return HourlyForecastCard(
                                        temperature:
                                            weather.hourlyTemperatures[index],
                                        weatherCode:
                                            weather.hourlyWeatherCode[index],
                                        iconService: _iconService,
                                        time:
                                            '${(index).toString().padLeft(2, '0')}:00');
                                  },
                                ),
                              ),
                              SizedBox(height: 20),
                              SizedBox(
                                height: 170,
                                child: ListView.separated(
                                  scrollDirection: Axis.horizontal,
                                  padding: EdgeInsets.symmetric(horizontal: 16),
                                  itemCount: weather.dailyWeatherCode.length
                                      .clamp(0, 4),
                                  separatorBuilder: (context, index) =>
                                      SizedBox(width: 12),
                                  itemBuilder: (context, index) {
                                    // Get the day name dynamically
                                    DateTime day = DateTime.now()
                                        .add(Duration(days: index));
                                    String dayName = [
                                      S.of(context).monday,
                                      S.of(context).tuesday,
                                      S.of(context).wednesday,
                                      S.of(context).thursday,
                                      S.of(context).friday,
                                      S.of(context).saturday,
                                      S.of(context).sunday
                                    ][day.weekday - 1];

                                    return DailyForecastCard(
                                      minTemp: weather.dailyTempMin[index],
                                      maxTemp: weather.dailyTempMax[index],
                                      weatherCode:
                                          weather.dailyWeatherCode[index],
                                      iconService: _iconService,
                                      day: dayName, // Translated dynamically
                                    );
                                  },
                                ),
                              ),
                              SizedBox(height: 20),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
              loading: () => Center(
                child: Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                ),
              ),
              error: (err, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      S.of(context).errorLoadingWeather, // Translated
                      style: TextStyle(color: Colors.white),
                    ),
                    SizedBox(height: 10),
                    ElevatedButton(
                      onPressed: () => ref.invalidate(weatherProvider),
                      child: Text(S.of(context).retry), // Translated
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
