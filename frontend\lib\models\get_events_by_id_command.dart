class EventsDetailsModel {
  final int id;
  final String? name;
  final String? description;
  final String category;
  final DateTime? startDate; // Added startDate field
  final List<ImageModel>? images;

  EventsDetailsModel({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    this.startDate, // Added startDate parameter
    this.images,
  });

  factory EventsDetailsModel.fromJson(Map<String, dynamic> json) {
    return EventsDetailsModel(
      id: json['id'] as int,
      name: json['name'] as String?,
      description: json['description'] as String?,
      category: json['category'] as String,
      startDate: json['startDate'] != null
          ? DateTime.parse(json['startDate'])
          : null, // Parse ISO string to DateTime if not null
      images: (json['images'] as List<dynamic>?)
          ?.map((image) => ImageModel.fromJson(image as Map<String, dynamic>))
          .toList(),
    );
  }

  // Method to convert EventsDetailsModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'startDate':
          startDate?.toIso8601String(), // Convert DateTime to ISO string
      'images': images?.map((image) => image.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'EventsDetailsModel(id: $id, name: $name, description: $description, category: $category, startDate: $startDate, images: $images)';
  }
}

class ImageModel {
  final int id;
  final String? path;
  final String? key;
  final String? preSignedUrl;
  final String? content;
  final bool isCover;

  ImageModel({
    required this.id,
    required this.path,
    required this.key,
    required this.preSignedUrl,
    required this.content,
    required this.isCover,
  });

  factory ImageModel.fromJson(Map<String, dynamic> json) {
    return ImageModel(
      id: json['id'] as int,
      path: json['path'] as String?,
      key: json['key'] as String?,
      preSignedUrl: json['preSignedUrl'] as String?,
      content: json['content'] as String?,
      isCover: json['isCover'] as bool,
    );
  }

  // Method to convert ImageModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'path': path,
      'key': key,
      'preSignedUrl': preSignedUrl,
      'content': content,
      'isCover': isCover,
    };
  }

  @override
  String toString() {
    return 'ImageModel(id: $id, path: $path, key: $key, preSignedUrl: $preSignedUrl, content: $content, isCover: $isCover)';
  }
}
