import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:frontend/models/register_command.dart';
import 'package:frontend/models/login_command.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class AuthenticationService {
  static final Dio _dio = Dio(BaseOptions(
    baseUrl: dotenv.env['BASE_URL']!,
    connectTimeout: const Duration(seconds: 10),
    receiveTimeout: const Duration(seconds: 10),
  ));

  /// **Register a new user**
  static Future<Response?> register(RegisterCommand command) async {
    const String registerEndpoint = 'Users/Register';

    try {
      final response = await _dio.post(
        registerEndpoint,
        data: command.toJson(),
      );

      if (response.statusCode == 200) {
        print("✅ Registration Successful: ${response.data}");
        return response;
      } else {
        print("❌ Registration Failed: Status ${response.statusCode}");
        print("🔴 Response Data: ${response.data}");
        throw Exception(
            "Registration failed with status ${response.statusCode}");
      }
    } on DioException catch (e) {
      print("❌ Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      return e.response;
    } catch (e) {
      print("⚠ Unexpected Error: $e");
      return null;
    }
  }

  /// **Login a user**
  static Future<Response?> login(LoginCommand command) async {
    const String loginEndpoint = 'Users/Login';

    try {
      final response = await _dio.post(
        loginEndpoint,
        data: command.toJson(),
      );

      if (response.statusCode == 200) {
        print("✅ Login Successful: ${response.data}");
        if (response.data['isSuccessful'] == true) {
          final accessToken = response.data['accessToken'];
          final refreshToken = response.data['refreshToken'];

          // ✅ Securely store the JWT tokens
          const secureStorage = FlutterSecureStorage();
          await secureStorage.write(key: 'accessToken', value: accessToken);
          await secureStorage.write(key: 'refreshToken', value: refreshToken);
        }
        return response;
      } else {
        print("❌ Login Failed: Status ${response.statusCode}");
        print("🔴 Response Data: ${response.data}");
        throw Exception("Login failed with status ${response.statusCode}");
      }
    } on DioException catch (e) {
      print("❌ Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      return e.response;
    } catch (e) {
      print("⚠ Unexpected Error: $e");
      return null;
    }
  }

  /// **Resend Verification Email**
  static Future<Response?> resendVerification(String email) async {
    const String resendEndpoint = 'Users/ResendConfirmationEmail';

    try {
      final response = await _dio.post(
        resendEndpoint,
        data: {"email": email},
      );

      if (response.statusCode == 204) {
        print("✅ Verification email resent successfully.");
        return response;
      } else {
        print("❌ Failed to resend verification email: ${response.statusCode}");
        print("🔴 Response Data: ${response.data}");
        throw Exception(
            "Resend verification failed with status ${response.statusCode}");
      }
    } on DioException catch (e) {
      print("❌ Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      return e.response;
    } catch (e) {
      print("⚠ Unexpected Error: $e");
      return null;
    }
  }

  /// **Verify Email (Confirm Email)**
  static Future<Response?> verifyEmail(
      String userId, String confirmationToken) async {
    const String verifyEmailEndpoint = 'Users/ConfirmEmail';

    try {
      final response = await _dio.put(
        verifyEmailEndpoint,
        data: {
          "userId": userId,
          "confirmationToken": confirmationToken,
        },
      );

      if (response.statusCode == 204) {
        print("✅ Email verified successfully (204 No Content)");
        return response;
      } else {
        print("❌ Email verification failed: Status ${response.statusCode}");
        print("🔴 Response Data: ${response.data}");
        throw Exception(
          "Email verification failed with status ${response.statusCode}",
        );
      }
    } on DioException catch (e) {
      print("❌ Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      return e.response;
    } catch (e) {
      print("⚠ Unexpected Error: $e");
      return null;
    }
  }
}
