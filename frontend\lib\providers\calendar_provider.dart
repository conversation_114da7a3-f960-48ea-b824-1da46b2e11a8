import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../domain/entities/custom_calendar/calendar_state.dart'; // Ensure this path is correct

part 'calendar_provider.g.dart';

/// This provider manages the calendar state including displayed month and selected date.
@riverpod
class CalendarNotifier extends _$CalendarNotifier {
  @override
  CalendarState build() {
    return CalendarState(
      displayedMonth: DateTime(DateTime.now().year, DateTime.now().month),
      selectedDate: null,
    );
  }

  /// Select a date in the calendar
  void selectDate(DateTime date) {
    state = state.copyWith(selectedDate: date);
  }

  /// Move to the previous month
  void previousMonth() {
    final current = state.displayedMonth;
    state = state.copyWith(
      displayedMonth: DateTime(current.year, current.month - 1),
    );
  }

  /// Move to the next month
  void nextMonth() {
    final current = state.displayedMonth;
    state = state.copyWith(
      displayedMonth: DateTime(current.year, current.month + 1),
    );
  }

  /// Reset to the current month
  void resetToCurrentMonth() {
    state = state.copyWith(
      displayedMonth: DateTime(DateTime.now().year, DateTime.now().month),
    );
  }
}
