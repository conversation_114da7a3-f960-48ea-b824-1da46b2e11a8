import 'package:flutter/material.dart';
import 'package:weather_icons/weather_icons.dart';

class WeatherIconService {
  static const Map<int, IconData> _weatherIcons = {
    0: WeatherIcons.day_sunny,
    1: WeatherIcons.day_sunny_overcast,
    2: WeatherIcons.day_cloudy_high,
    3: WeatherIcons.cloud,
    45: WeatherIcons.fog,
    48: WeatherIcons.fog,
    51: WeatherIcons.sprinkle,
    53: WeatherIcons.sprinkle,
    55: WeatherIcons.sprinkle,
    56: WeatherIcons.rain_mix,
    57: WeatherIcons.rain_mix,
    61: WeatherIcons.rain,
    63: WeatherIcons.rain,
    65: WeatherIcons.rain,
    66: WeatherIcons.showers,
    67: WeatherIcons.showers,
    71: WeatherIcons.snow,
    73: WeatherIcons.snow,
    75: WeatherIcons.snow,
    77: WeatherIcons.snowflake_cold,
    80: WeatherIcons.showers,
    81: WeatherIcons.showers,
    82: WeatherIcons.showers,
    85: WeatherIcons.snow_wind,
    86: WeatherIcons.snow_wind,
    95: WeatherIcons.thunderstorm,
    96: WeatherIcons.thunderstorm,
    99: WeatherIcons.thunderstorm,
  };

  IconData showWeatherIcon(int code) {
    return _weatherIcons[code] ?? WeatherIcons.na;
  }
}
