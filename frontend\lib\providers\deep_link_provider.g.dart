// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'deep_link_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$deepLinkHash() => r'332af0ce4d213d6bf8a359b14dbbccf6215d71fd';

/// See also [DeepLink].
@ProviderFor(DeepLink)
final deepLinkProvider =
    AutoDisposeAsyncNotifierProvider<DeepLink, Uri?>.internal(
  DeepLink.new,
  name: r'deepLinkProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$deepLinkHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeepLink = AutoDisposeAsyncNotifier<Uri?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
