import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:go_router/go_router.dart';
import 'package:curved_navigation_bar/curved_navigation_bar.dart';
import '../../providers/app_providers.dart';
import '../../l10n/generated/l10n.dart';

class CustomBottomNavBar extends ConsumerWidget {
  const CustomBottomNavBar({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ref.watch(themeNotifierProvider);
    final backTheme = Theme.of(context);

    final selectedIndex = ref.watch(selectedIndexProvider);
    final List<String> paths = [
      '/favorites',
      '/location',
      '/home',
      '/messages',
      '/settings',
    ];

    // Determine the current theme mode to adjust the bottom nav bar
    final isDarkMode = theme == ThemeMode.dark;

    void navigateToIndex(int index) {
      context.go(paths[index]);
      ref.read(selectedIndexProvider.notifier).setIndex(index);
    }

    return CurvedNavigationBar(
      index: selectedIndex,
      height: 60,
      backgroundColor: Colors.transparent,
      color: isDarkMode
          ? Color(0xFF011F38)
          : Color.fromRGBO(243, 242, 242, 1), // Dark mode background
      buttonBackgroundColor: const Color(0xFF22D400),
      animationDuration: const Duration(milliseconds: 300),
      animationCurve: Curves.easeInOut,
      items: [
        Icon(
          Icons.star,
          size: 30,
          color: selectedIndex == 0
              ? Colors.white // White if selected
              : (isDarkMode
                  ? Colors.grey.shade600
                  : Colors.grey.shade400), // Grey if not selected
        ),
        Icon(
          Icons.location_on_outlined,
          size: 30,
          color: selectedIndex == 1
              ? Colors.white // White if selected
              : (isDarkMode
                  ? Colors.grey.shade600
                  : Colors.grey.shade400), // Grey if not selected
        ),
        Icon(
          Icons.home,
          size: 30,
          color: selectedIndex == 2
              ? Colors.white // White if selected
              : (isDarkMode
                  ? Colors.grey.shade600
                  : Colors.grey.shade400), // Grey if not selected
        ),
        Icon(
          Icons.mail_outline,
          size: 30,
          color: selectedIndex == 3
              ? Colors.white // White if selected
              : (isDarkMode
                  ? Colors.grey.shade600
                  : Colors.grey.shade400), // Grey if not selected
        ),
        Icon(
          Icons.settings,
          size: 30,
          color: selectedIndex == 4
              ? Colors.white // White if selected
              : (isDarkMode
                  ? Colors.grey.shade600
                  : Colors.grey.shade400), // Grey if not selected
        ),
      ],
      onTap: navigateToIndex,
    );
  }
}
