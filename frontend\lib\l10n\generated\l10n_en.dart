import 'l10n.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class SEn extends S {
  SEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Smart Yambol';

  @override
  String get guest => 'Guest';

  @override
  String get settings => 'Settings';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get language => 'Language';

  @override
  String get terms => 'Terms & Conditions';

  @override
  String get login => 'Login';

  @override
  String get tourism => 'Tourism';

  @override
  String get tourismDescription => 'Explore the beauty of Yambol and create lifelong memories!';

  @override
  String get weather => 'Weather';

  @override
  String get weatherDescription => 'Stay prepared! See the forecast and air quality.';

  @override
  String get events => 'Events';

  @override
  String get eventsDescription => 'Check out cultural and sports events in the city.';

  @override
  String get repairs => 'Repairs';

  @override
  String get repairsDescription => 'Stay informed! See where and when repairs are happening.';

  @override
  String get parking => 'Parking';

  @override
  String get parkingDescription => 'Find a parking spot or pay for blue zone parking via SMS.';

  @override
  String get generalInfo => 'General Info';

  @override
  String get generalInfoDescription => 'Discover everything you need to know about Yambol in one place.';

  @override
  String get transport => 'Transport';

  @override
  String get transportDescription => 'Up-to-date information on public transport routes and schedules.';

  @override
  String get cameras => 'Cameras';

  @override
  String get camerasDescription => 'Live-stream views from key locations in the city.';

  @override
  String get news => 'News';

  @override
  String get newsDescription => 'Stay updated! Follow the latest news and events.';

  @override
  String get taxes => 'Taxes';

  @override
  String get taxesDescription => 'View and pay your municipal taxes quickly and easily.';

  @override
  String get publicServices => 'Services';

  @override
  String get publicServicesDescription => 'Take advantage of the services we offer!';

  @override
  String get reports => 'Reports';

  @override
  String get reportsDescription => 'Report an issue in the city and help make it better.';

  @override
  String get favorites => 'Favorites';

  @override
  String get location => 'Location';

  @override
  String get home => 'Home';

  @override
  String get messages => 'Messages';

  @override
  String get emailOrUsername => 'Email or Username';

  @override
  String get password => 'Password';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get loginButton => 'Log In';

  @override
  String get orContinueWith => '- or continue with -';

  @override
  String get createNewAccount => 'Create a new account';

  @override
  String get registerHere => 'here';

  @override
  String get register => 'Register';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get agreeToTerms => 'By clicking the Register button, you agree to the';

  @override
  String get registerTerms => 'terms & conditions';

  @override
  String get registerButton => 'Register';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get loginHere => 'Log in here';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get firstName => 'First Name';

  @override
  String get lastName => 'Last Name';

  @override
  String get email => 'Email';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get invalidEmail => 'Please enter a valid email';

  @override
  String get registrationSuccessful => 'Registration successful!';

  @override
  String get passwordRequired => 'Password is required';

  @override
  String get passwordTooShort => 'Password is too short';

  @override
  String get confirmPasswordRequired => 'Confirm password required';

  @override
  String get passwordsDoNotMatch => 'Passwords do not match';

  @override
  String get firstNameRequired => 'First name is required';

  @override
  String get lastNameRequired => 'Last name is required';

  @override
  String get tourismWelcomeMessage => 'Explore Yambol! Discover its landmarks and get inspired by its history.';

  @override
  String get tourismLegendsAndMyths => 'Legends and Myths';

  @override
  String get tourismSights => 'Landmarks';

  @override
  String get tourismCulturalAndArtisticSites => 'Cultural and Artistic Sites';

  @override
  String get tourismRoutesAndActivities => 'Routes and Activities';

  @override
  String get tourismFamilyEntertainment => 'Family Entertainment';

  @override
  String get tourismNightlife => 'Nightlife';

  @override
  String get tourismTransport => 'Transport';

  @override
  String get tourismTravelAgencies => 'Travel Agencies';

  @override
  String get loginSuccess => 'Login successful';

  @override
  String get loginError => 'Login failed';

  @override
  String get legendsAndMythsWelcomeMessage => 'Discover the mysteries of Yambol: Legends and myths that will captivate you';

  @override
  String get weatherTitle => 'Weather';

  @override
  String get cityName => 'Yambol';

  @override
  String get wind => 'Wind';

  @override
  String get humidity => 'Humidity';

  @override
  String get errorLoadingWeather => 'Error loading weather data';

  @override
  String get retry => 'Retry';

  @override
  String get monday => 'Monday';

  @override
  String get tuesday => 'Tuesday';

  @override
  String get wednesday => 'Wednesday';

  @override
  String get thursday => 'Thursday';

  @override
  String get friday => 'Friday';

  @override
  String get saturday => 'Saturday';

  @override
  String get sunday => 'Sunday';

  @override
  String get eventsWelcomeMessage => 'Don\'t miss anything! Explore cultural and sports events in the city!';

  @override
  String get search => 'Search...';

  @override
  String get tourismCulturalSites => 'Cultural sites';

  @override
  String get map => 'Map';

  @override
  String get all => 'All';

  @override
  String get sportEvents => 'Sport';

  @override
  String get cultureEvents => 'Culture';

  @override
  String get celebrationEvents => 'Celebrations';

  @override
  String get mon => 'Mon';

  @override
  String get tue => 'Tue';

  @override
  String get wed => 'Wed';

  @override
  String get thu => 'Thu';

  @override
  String get fri => 'Fri';

  @override
  String get sat => 'Sat';

  @override
  String get sun => 'Sun';

  @override
  String get january => 'January';

  @override
  String get february => 'February';

  @override
  String get march => 'March';

  @override
  String get april => 'April';

  @override
  String get may => 'May';

  @override
  String get june => 'June';

  @override
  String get july => 'July';

  @override
  String get august => 'August';

  @override
  String get september => 'September';

  @override
  String get october => 'October';

  @override
  String get november => 'November';

  @override
  String get december => 'December';

  @override
  String get emailConfirmationTitle => 'Email Confirmation';

  @override
  String get emailConfirmationMessage => 'Please check your email to confirm your account.';

  @override
  String get didNotReceiveEmail => 'Didn\'t receive the email?';

  @override
  String get resendVerificationEmail => 'Resend Verification Email';

  @override
  String get verificationEmailResent => 'Verification email resent!';

  @override
  String get errorOccurred => 'Error occurred';

  @override
  String get emailVerificationTitle => 'Email Verification';

  @override
  String get verifyEmailButton => 'Verify Email';

  @override
  String get emailVerifiedSuccessfully => 'Email verified successfully!';

  @override
  String get generalInfoWelcomeMessage => 'Yambol – business, education, and essential city information!';

  @override
  String get gasStations => 'Gas stations';

  @override
  String get generalInformationFull => 'General information';

  @override
  String get shop => 'Shops';

  @override
  String get restaurants => 'Restaurants';

  @override
  String get coffee => 'Coffee';

  @override
  String get bars => 'Bars';

  @override
  String get pastryShops => 'Pastry shops';

  @override
  String get establishments => 'Establishments';

  @override
  String get establishmentsWelcomeMessage => 'Cozy restaurants, traditional cuisine, and modern venues with a unique atmosphere!';

  @override
  String get hotels => 'Hotels';

  @override
  String get guestHouses => 'Guest houses';

  @override
  String get accommodation => 'Accommodation';

  @override
  String get accommodationWelcomeMessage => 'Comfortable hotels, cozy guesthouses, and great places to stay for every taste!';

  @override
  String get finance => 'Finance';

  @override
  String get banks => 'Banks';

  @override
  String get currencyExchanges => 'Currency Exchange';

  @override
  String get insuranceCompanies => 'Insurance';

  @override
  String get atms => 'ATMs';

  @override
  String get financeYambolWelcomeMessage => 'Secure banking services, affordable loans, and financial solutions for you in Yambol!';

  @override
  String get bioShops => 'Bio shops';

  @override
  String get farms => 'Farms';

  @override
  String get recycling => 'Recycling';

  @override
  String get ecoInitiatives => 'Eco initiatives';

  @override
  String get ecology => 'Ecology';

  @override
  String get ecoYambolWelcomeMessage => 'Clean nature, sustainable future, and eco-friendly solutions for Yambol!';

  @override
  String get culture => 'Culture';

  @override
  String get museums => 'Museums';

  @override
  String get theaters => 'Theaters';

  @override
  String get galleries => 'Galleries';

  @override
  String get cultureYambolWelcomeMessage => 'Rich cultural heritage, inspiring art, and traditions in Yambol!';

  @override
  String get education => 'Education';

  @override
  String get kindergardens => 'Kindergardens';

  @override
  String get nursery => 'Nursery';

  @override
  String get childNutritionCenter => 'Child nutrition center';

  @override
  String get schools => 'Schools';

  @override
  String get universities => 'Universities';

  @override
  String get developmentCenters => 'Development centers';

  @override
  String get health => 'Health';

  @override
  String get pharmacies => 'Pharmacies';

  @override
  String get medicalEstablishments => 'Medical establishments';

  @override
  String get doctorsOffices => 'Doctors offices';

  @override
  String get medicalLabs => 'Medical labs';

  @override
  String get veterinaries => 'Veterinaries';

  @override
  String get healthYambolWelcomeMessage => 'Health and care for everyone – quality medical services and a healthy lifestyle in Yambol!';

  @override
  String get educationYambolWelcomeMessage => 'Quality education for a bright future – innovative learning and development in Yambol!';

  @override
  String get sport => 'Sport';

  @override
  String get sportFacilities => 'Sport Facilities';

  @override
  String get sportYambolWelcomeMessage => 'Sports and an active lifestyle – modern facilities and opportunities for movement in Yambol!';

  @override
  String get newsYambolWelcomeMessage => 'Stay informed! Follow the latest news and events.';

  @override
  String get close => 'Close';

  @override
  String get description => 'Description';

  @override
  String get municipalityTaxes => 'Municipality Taxes';

  @override
  String get information => 'Information';

  @override
  String get chooseWayToPay => 'You can pay online using your CIN or with an electronic signature (which must be installed on your device).';

  @override
  String get waysToPay => 'Ways to pay';

  @override
  String get kinNumber => 'Pay with KIN';

  @override
  String get kinDesc => 'Fast and convenient payment with your KIN number';

  @override
  String get electronicSignature => 'Pay with electronic signature';

  @override
  String get electronicSignatureDesc => 'Secure payment with your electronic signature';

  @override
  String get userHelp => 'Need help? Contact us at phone: 0875 333 844';

  @override
  String get problemElectronicSignature => 'The electronic signature payment link could not be opened.';

  @override
  String get problemKin => 'The KIN payment link could not be opened.';

  @override
  String get navigate => 'Navigate';

  @override
  String get business => 'Business';

  @override
  String get landmarks => 'Landmarks';

  @override
  String get healthcare => 'Healthcare';

  @override
  String get sports => 'Sports';

  @override
  String get applyFilters => 'Apply Filters';

  @override
  String get filterCategories => 'Filter Categories';
}
