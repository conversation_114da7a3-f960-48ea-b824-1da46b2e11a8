// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'filter_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$filterStateHash() => r'9849fca24456f8ddd23b5c6f0bebe2d651ef805f';

/// See also [FilterState].
@ProviderFor(FilterState)
final filterStateProvider =
    AutoDisposeNotifierProvider<FilterState, FilterType>.internal(
  FilterState.new,
  name: r'filterStateProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$filterStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$FilterState = AutoDisposeNotifier<FilterType>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
