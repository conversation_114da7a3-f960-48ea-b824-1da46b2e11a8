import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/l10n/generated/l10n.dart';
import 'package:frontend/models/get_news_command.dart';
import 'package:frontend/presentation/screens/news/news_details_screen.dart';
import 'package:frontend/providers/theme_provider.dart';
import 'package:frontend/providers/news_provider.dart'; // Add this import
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart'; // For date formatting

class NewsScreen extends ConsumerStatefulWidget {
  const NewsScreen({super.key});

  @override
  ConsumerState<NewsScreen> createState() => _NewsScreenState();
}

class _NewsScreenState extends ConsumerState<NewsScreen> {
  @override
  void initState() {
    super.initState();
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    super.dispose();
  }

  String formatDate(DateTime date) {
    return DateFormat('dd.MM.yyyy г.').format(date);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final themeMode = ref.watch(themeNotifierProvider);
    final isDarkMode = themeMode == ThemeMode.dark;

    // Watch the news data using the provider
    final newsAsync = ref.watch(fetchAllNewsProvider);

    final imageHeight = MediaQuery.of(context).size.height * 0.20;
    final overlapAmount = 40.0;

    return Container(
      color: theme.scaffoldBackgroundColor,
      child: Stack(
        children: [
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: imageHeight,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/images/news-image.png'),
                  fit: BoxFit.cover,
                  colorFilter: ColorFilter.mode(
                    isDarkMode
                        ? Colors.white.withValues(alpha: 0.3)
                        : Color(0x3322D400),
                    BlendMode.darken,
                  ),
                ),
              ),
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.only(top: 0),
                  child: Text(
                    S.of(context).newsYambolWelcomeMessage,
                    textAlign: TextAlign.center,
                    style: GoogleFonts.roboto(
                      fontWeight: FontWeight.w700,
                      fontSize: 17,
                      height: 15 / 14,
                      letterSpacing: 0.0,
                      color: isDarkMode ? Colors.white : Color(0xFF626262),
                    ),
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            top: 16,
            right: 16,
            child: Icon(
              Icons.star,
              color: isDarkMode ? Colors.white : Color(0xFF626262),
              size: 30,
            ),
          ),
          // News List - positioned to overlap with the image
          Positioned(
            top: imageHeight - overlapAmount,
            left: 0,
            right: 0,
            bottom: 0,
            child: newsAsync.when(
              data: (newsList) =>
                  _buildNewsContent(newsList, isDarkMode, context),
              loading: () => Center(
                child: CircularProgressIndicator(
                  color: isDarkMode ? Colors.white : Color(0xFF22D400),
                ),
              ),
              error: (error, stackTrace) =>
                  _buildErrorContent(isDarkMode, context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNewsContent(
      List<NewsModel> newsList, bool isDarkMode, BuildContext context) {
    if (newsList.isEmpty) {
      return Center(
        child: Text(
          'No news available',
          style: GoogleFonts.roboto(
            fontSize: 16,
            color: isDarkMode ? Colors.white : Color(0xFF626262),
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        // Refresh by invalidating the provider
        ref.invalidate(fetchAllNewsProvider);
      },
      child: ListView.builder(
        padding: EdgeInsets.only(top: 0),
        itemCount: newsList.length,
        itemBuilder: (context, index) {
          final NewsModel newsItem = newsList[index];

          return Container(
            margin: EdgeInsets.only(bottom: 20),
            padding: EdgeInsets.only(top: 14),
            child: GestureDetector(
              onTap: () async {
                final url = Uri.parse(newsItem.link);
                if (await canLaunchUrl(url)) {
                  await launchUrl(url, mode: LaunchMode.externalApplication);
                } else {
                  // Handle error: URL can't be launched
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Could not launch the news link.')),
                  );
                }
              },
              child: Container(
                margin: EdgeInsets.only(right: 17),
                width: MediaQuery.of(context).size.width * 0.9,
                decoration: BoxDecoration(
                  color: isDarkMode ? Color(0xFF333333) : Colors.white,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(10),
                    bottomRight: Radius.circular(10),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: isDarkMode
                          ? Colors.black.withValues(alpha: 0.2)
                          : Colors.black.withValues(alpha: 0.1),
                      offset: Offset(4, 4),
                      blurRadius: 6,
                    ),
                  ],
                ),
                child: Stack(
                  clipBehavior: Clip.none,
                  children: [
                    // Content with heading and description
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Heading
                          Padding(
                            padding: const EdgeInsets.only(top: 10.0),
                            child: Text(
                              newsItem.title,
                              style: GoogleFonts.roboto(
                                fontWeight: FontWeight.w700,
                                fontSize: 16,
                                height: 1.2,
                                letterSpacing: 0.15,
                                color: isDarkMode
                                    ? Colors.white
                                    : Color(0xFF424242),
                              ),
                            ),
                          ),
                          // Description with ellipsis
                          Padding(
                            padding:
                                const EdgeInsets.only(top: 8.0, bottom: 8.0),
                            child: Text(
                              newsItem.summary,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 4,
                              style: GoogleFonts.roboto(
                                fontWeight: FontWeight.w400,
                                fontSize: 14,
                                height: 1.2,
                                letterSpacing: 0.0,
                                color: isDarkMode
                                    ? Colors.white.withValues(alpha: 0.8)
                                    : Color(0xFF626262),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Date container at the top-right of the list item
                    Positioned(
                      top: -14,
                      right: 10,
                      child: Container(
                        width: 101.136,
                        height: 29,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(50),
                            bottomLeft: Radius.circular(50),
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.25),
                              offset: Offset(0, 0),
                              blurRadius: 10,
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            formatDate(newsItem.publishedDate),
                            style: GoogleFonts.roboto(
                              fontWeight: FontWeight.w700,
                              fontSize: 12.5,
                              color: Colors.black,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildErrorContent(bool isDarkMode, BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: isDarkMode ? Colors.white : Color(0xFF626262),
          ),
          SizedBox(height: 16),
          Text(
            'Failed to load news',
            style: GoogleFonts.roboto(
              fontSize: 16,
              color: isDarkMode ? Colors.white : Color(0xFF626262),
            ),
          ),
          SizedBox(height: 8),
          ElevatedButton(
            onPressed: () {
              // Retry by invalidating the provider
              ref.invalidate(fetchAllNewsProvider);
            },
            child: Text('Retry'),
          ),
        ],
      ),
    );
  }
}
