import 'package:flutter/material.dart';
import '../../l10n/generated/l10n.dart';

class RegisterForm extends StatelessWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController emailController;
  final TextEditingController passwordController;
  final TextEditingController confirmPasswordController;
  final TextEditingController firstNameController;
  final TextEditingController lastNameController;

  const RegisterForm({
    Key? key,
    required this.formKey,
    required this.emailController,
    required this.passwordController,
    required this.confirmPasswordController,
    required this.firstNameController,
    required this.lastNameController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Form(
      key: formKey, // ✅ Attach form key
      child: Column(
        children: [
          // ✅ First Name Field
          _buildTextField(
            context,
            controller: firstNameController,
            label: S.of(context).firstName,
            icon: Icons.person,
            validator: (value) =>
                value!.isEmpty ? S.of(context).firstNameRequired : null,
          ),
          const SizedBox(height: 12),

          // ✅ Last Name Field
          _buildTextField(
            context,
            controller: lastNameController,
            label: S.of(context).lastName,
            icon: Icons.person_outline,
            validator: (value) =>
                value!.isEmpty ? S.of(context).lastNameRequired : null,
          ),
          const SizedBox(height: 12),

          // ✅ Email Field
          _buildTextField(
            context,
            controller: emailController,
            label: S.of(context).email,
            icon: Icons.email,
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value!.isEmpty) {
                return S.of(context).emailRequired;
              }
              final emailRegex =
                  RegExp(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$");
              if (!emailRegex.hasMatch(value)) {
                return S.of(context).invalidEmail;
              }
              return null;
            },
          ),
          const SizedBox(height: 12),

          // ✅ Password Field
          _buildTextField(
            context,
            controller: passwordController,
            label: S.of(context).password,
            icon: Icons.lock,
            isPassword: true,
            validator: (value) {
              if (value!.isEmpty) {
                return S.of(context).passwordRequired;
              }
              if (value.length < 6) {
                return S.of(context).passwordTooShort;
              }
              return null;
            },
          ),
          const SizedBox(height: 12),

          // ✅ Confirm Password Field
          _buildTextField(
            context,
            controller: confirmPasswordController,
            label: S.of(context).confirmPassword,
            icon: Icons.lock_outline,
            isPassword: true,
            validator: (value) {
              if (value!.isEmpty) {
                return S.of(context).confirmPasswordRequired;
              }
              if (value != passwordController.text) {
                return S.of(context).passwordsDoNotMatch;
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  /// **Reusable Input Field Widget**
  Widget _buildTextField(
    BuildContext context, {
    required TextEditingController controller,
    required String label,
    required IconData icon,
    bool isPassword = false,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: isPassword,
      keyboardType: keyboardType,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: Colors.green),
        filled: true,
        fillColor: Colors.grey.shade200,
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Colors.green),
        ),
      ),
    );
  }
}
