class InterestPointDetailsModel {
  final int id;
  final String? name;
  final String? description;
  final String category;
  final List<ImageModel>? images;

  InterestPointDetailsModel({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    this.images,
  });

  factory InterestPointDetailsModel.fromJson(Map<String, dynamic> json) {
    return InterestPointDetailsModel(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      images: (json['images'] as List<dynamic>?)
          ?.map((image) => ImageModel.fromJson(image as Map<String, dynamic>))
          .toList(),
    );
  }
}

class ImageModel {
  final int id;
  final String? path;
  final String? key;
  final String? preSignedUrl;
  final String? content;
  final bool isCover;

  ImageModel({
    required this.id,
    required this.path,
    required this.key,
    required this.preSignedUrl,
    required this.content,
    required this.isCover,
  });

  factory ImageModel.fromJson(Map<String, dynamic> json) {
    return ImageModel(
      id: json['id'] as int,
      path: json['path'] as String?,
      key: json['key'] as String?,
      preSignedUrl: json['preSignedUrl'] as String?,
      content: json['content'] as String?,
      isCover: json['isCover'] as bool,
    );
  }
}
