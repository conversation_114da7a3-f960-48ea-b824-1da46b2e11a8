// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'news_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$fetchAllNewsHash() => r'553df0a7fcc6bb88c8f28e46d70988ee0a5bcb16';

/// See also [fetchAllNews].
@ProviderFor(fetchAllNews)
final fetchAllNewsProvider =
    AutoDisposeFutureProvider<List<NewsModel>>.internal(
  fetchAllNews,
  name: r'fetchAllNewsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$fetchAllNewsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef FetchAllNewsRef = AutoDisposeFutureProviderRef<List<NewsModel>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
