import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:frontend/models/get_interest_points_command.dart';

class InterestPointsService {
  static final Dio _dio = Dio(BaseOptions(
    baseUrl: dotenv.env['BASE_URL']!, // Load BASE_URL from .env
  ));

  /// **Fetch Items**
  static Future<List<InterestPointsGetModel>> fetchItems({
    List<String>? categories, // Change category to List<String>
    String? search,
    String? languageCode,
  }) async {
    const String itemsEndpoint = 'InterestPoints'; // Adjust if needed

    // Build query parameters dynamically based on the inputs
    final Map<String, dynamic> queryParams = {};

    // Check if categories is not null or empty and then add each category to queryParams
    if (categories != null && categories.isNotEmpty) {
      queryParams['categories'] =
          categories; // Concatenate categories with commas
    }
    if (search != null && search.isNotEmpty) {
      queryParams['search'] = search;
    }
    if (languageCode != null && languageCode.isNotEmpty) {
      queryParams['languageCode'] = languageCode;
    }

    try {
      print('PARAMS FOR REQUEST $queryParams');

      final response =
          await _dio.get(itemsEndpoint, queryParameters: queryParams);

      if (response.statusCode == 200) {
        print(response.data);
        List<dynamic> jsonData = response.data;
        return jsonData
            .map((item) => InterestPointsGetModel.fromJson(item))
            .toList();
      } else {
        throw Exception(
            'Failed to load items with status ${response.statusCode}');
      }
    } on DioException catch (e) {
      print("❌ Dio Error: ${e.response?.statusCode}");
      print("📩 Response Data: ${e.response?.data}");
      print("📝 Error Message: ${e.message}");
      return [];
    } catch (e) {
      print("⚠ Unexpected Error: $e");
      return [];
    }
  }
}
