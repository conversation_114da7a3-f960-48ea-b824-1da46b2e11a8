import 'dart:convert';
import 'dart:typed_data';

class RegisterCommand {
  final String email;
  final String password;
  final String confirmPassword;
  final String firstName;
  final String lastName;
  final Uint8List? profilePicture; // ✅ Optional image data

  RegisterCommand({
    required this.email,
    required this.password,
    required this.confirmPassword,
    required this.firstName,
    required this.lastName,
    this.profilePicture,
  });

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      "email": email,
      "password": password,
      "confirmPassword": confirmPassword,
      "firstName": firstName,
      "lastName": lastName,
      "profilePicture":
          profilePicture != null ? base64Encode(profilePicture!) : null,
    };
  }
}
