import 'package:flutter/material.dart';
import 'package:frontend/presentation/widgets/interest_points_reusable.dart';

class TourismTravelAgencies extends StatelessWidget {
  const TourismTravelAgencies({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset:
          true, // This ensures the screen resizes when the keyboard shows up
      body: SingleChildScrollView(
        // Allows scrolling when keyboard opens
        child: Column(
          children: [
            // Dynamically adjust the map's size
            SizedBox(
              height: MediaQuery.of(context).size.height *
                  0.83, // Map will take up 72% of screen height
              child: MapWithSearchAndData(
                  categories: ['TouristAgency'], // Pass the category 'Tourist',
                  path: 'toruistAgency',
                  type: 'tourism',
                  mapHeight: MediaQuery.of(context).size.height *
                      0.25, // Set map height to 25% of screen height
                  iconPath: 'assets/icons/travel-agency-icon.svg'),
            ),
            // Add the rest of your UI components below the map as needed
          ],
        ),
      ),
    );
  }
}
