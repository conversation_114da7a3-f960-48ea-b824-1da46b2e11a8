import 'package:flutter/material.dart';
import 'package:frontend/presentation/widgets/interest_points_reusable.dart'; // Assuming this widget includes your map and search bar

class TourismCulturalSites extends StatelessWidget {
  const TourismCulturalSites({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset:
          true, // Ensures the screen resizes when keyboard shows up
      body: SingleChildScrollView(
        // Allows scrolling when keyboard opens
        child: Column(
          children: [
            // Dynamically adjust the map's size
            SizedBox(
              height: MediaQuery.of(context).size.height *
                  0.83, // Map takes 72% of screen height
              child: MapWithSearchAndData(
                categories: ['Culture'], // Set the category to 'Cultural'
                path: 'culture',
                type: 'tourism',
                mapHeight: MediaQuery.of(context).size.height *
                    0.25, // Map height set to 25% of screen height
                iconPath:
                    'assets/icons/culture-icon.svg', // Update with relevant icon
              ),
            ),
            // Positioned search bar
          ],
        ),
      ),
    );
  }
}
