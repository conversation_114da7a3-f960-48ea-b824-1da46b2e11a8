import 'package:frontend/presentation/screens/confirm/confirm_email_screen.dart';
import 'package:frontend/presentation/screens/confirm/email_verification_screen.dart';
import 'package:frontend/presentation/screens/events/events_details_screen.dart';
import 'package:frontend/presentation/screens/events/events_screen.dart';
import 'package:frontend/presentation/screens/general-information/accommodations/accommodations_screen.dart';
import 'package:frontend/presentation/screens/general-information/accommodations/guest_houses.dart';
import 'package:frontend/presentation/screens/general-information/accommodations/hotels.dart';
import 'package:frontend/presentation/screens/general-information/culture/culture_screen.dart';
import 'package:frontend/presentation/screens/general-information/culture/galleries.dart';
import 'package:frontend/presentation/screens/general-information/culture/museums.dart';
import 'package:frontend/presentation/screens/general-information/culture/theaters.dart';
import 'package:frontend/presentation/screens/general-information/ecology/bio_shops.dart';
import 'package:frontend/presentation/screens/general-information/ecology/eco_initiatives.dart';
import 'package:frontend/presentation/screens/general-information/ecology/ecology_screen.dart';
import 'package:frontend/presentation/screens/general-information/ecology/farms.dart';
import 'package:frontend/presentation/screens/general-information/ecology/recycling.dart';
import 'package:frontend/presentation/screens/general-information/education/child_nutrition_center.dart';
import 'package:frontend/presentation/screens/general-information/education/development_centers.dart';
import 'package:frontend/presentation/screens/general-information/education/education_screen.dart';
import 'package:frontend/presentation/screens/general-information/education/kindergardens.dart';
import 'package:frontend/presentation/screens/general-information/education/nurseries.dart';
import 'package:frontend/presentation/screens/general-information/education/schools.dart';
import 'package:frontend/presentation/screens/general-information/education/universities.dart';
import 'package:frontend/presentation/screens/general-information/establishments/bars.dart';
import 'package:frontend/presentation/screens/general-information/establishments/coffes.dart';
import 'package:frontend/presentation/screens/general-information/establishments/establishments.dart';
import 'package:frontend/presentation/screens/general-information/establishments/pastry_shop.dart';
import 'package:frontend/presentation/screens/general-information/establishments/restaurants.dart';
import 'package:frontend/presentation/screens/general-information/finance/atms.dart';
import 'package:frontend/presentation/screens/general-information/finance/bank.dart';
import 'package:frontend/presentation/screens/general-information/finance/currency_exchange.dart';
import 'package:frontend/presentation/screens/general-information/finance/finance_screen.dart';
import 'package:frontend/presentation/screens/general-information/finance/insurance_companies.dart';
import 'package:frontend/presentation/screens/general-information/gas_station.dart';
import 'package:frontend/presentation/screens/general-information/general_information_screen.dart';
import 'package:frontend/presentation/screens/general-information/health/doctors_offices.dart';
import 'package:frontend/presentation/screens/general-information/health/health_screen.dart';
import 'package:frontend/presentation/screens/general-information/health/medical_establishments.dart';
import 'package:frontend/presentation/screens/general-information/health/medical_labs.dart';
import 'package:frontend/presentation/screens/general-information/health/pharmacies.dart';
import 'package:frontend/presentation/screens/general-information/health/veterinaries.dart';
import 'package:frontend/presentation/screens/general-information/shop.dart';
import 'package:frontend/presentation/screens/general-information/sport/sport_facilities.dart';
import 'package:frontend/presentation/screens/general-information/sport/sport_screen.dart';
import 'package:frontend/presentation/screens/login_screen.dart';
import 'package:frontend/presentation/screens/municipality-taxes/municipality_tax.dart';
import 'package:frontend/presentation/screens/news/news_screen.dart';
import 'package:frontend/presentation/screens/register_screen.dart';
import 'package:frontend/presentation/screens/tourism/tourism_cultural_and_artistic_sites.dart';
import 'package:frontend/presentation/screens/tourism/tourism_family_ent.dart';
import 'package:frontend/presentation/screens/tourism/tourism_landmarks.dart';
import 'package:frontend/presentation/screens/tourism/tourism_legends_and_myths_screen.dart';
import 'package:frontend/presentation/screens/tourism/tourism_map_screen.dart';
import 'package:frontend/presentation/screens/weather/weather_screen.dart';
import 'package:frontend/presentation/screens/tourism/tourism_night_life.dart';
import 'package:frontend/presentation/screens/tourism/tourism_routes_and_activities.dart';
import 'package:frontend/presentation/screens/tourism/tourism_transport.dart';
import 'package:frontend/presentation/screens/tourism/tourism_travel_agencies.dart';
import 'package:frontend/presentation/widgets/interest_points_details.dart';
import 'package:go_router/go_router.dart';
import 'package:frontend/presentation/screens/main_screen.dart';
import 'package:frontend/presentation/screens/favorites_screen.dart';
import 'package:frontend/presentation/screens/location_screen.dart';
import 'package:frontend/presentation/screens/smart_yambol_screen.dart';
import 'package:frontend/presentation/screens/messages_screen.dart';
import 'package:frontend/presentation/screens/settings_screen.dart';
import 'package:frontend/presentation/screens/tourism/tourism_screen.dart';

final GoRouter router = GoRouter(
  initialLocation: '/home', // Default screen when the app starts
  routes: [
    ShellRoute(
      builder: (context, state, child) {
        return MainScreen(child: child); // Pass child screen to MainScreen
      },
      routes: [
        GoRoute(
          path: '/favorites',
          builder: (context, state) => const FavoritesScreen(),
        ),
        GoRoute(
          path: '/location',
          builder: (context, state) => const LocationScreen(),
        ),
        GoRoute(
          path: '/home',
          builder: (context, state) => const SmartYambolScreen(),
        ),
        GoRoute(
          path: '/messages',
          builder: (context, state) => const MessagesScreen(),
        ),
        GoRoute(
          path: '/settings',
          builder: (context, state) => const SettingsScreen(),
        ),
        GoRoute(
          path: '/tourism',
          builder: (context, state) => const TourismScreen(),
        ),
        GoRoute(
          path: '/tourism/legends-and-myths',
          builder: (context, state) => const TourismLegendsAndMythsScreen(),
        ),
        GoRoute(
          path: '/tourism/tourism-map',
          builder: (context, state) => const TourismMapScreen(),
        ),
        GoRoute(
          path: '/tourism/landmarks',
          builder: (context, state) => const TourismLandmarks(),
        ),
        GoRoute(
          path: '/tourism/cultural-and-artistic-sites',
          builder: (context, state) => const TourismCulturalSites(),
        ),
        GoRoute(
          path: '/tourism/routes-and-activities',
          builder: (context, state) => const TourismActivities(),
        ),
        GoRoute(
          path: '/tourism/family-entertainment',
          builder: (context, state) => const TourismFamilyEntertainment(),
        ),
        GoRoute(
          path: '/tourism/night-life',
          builder: (context, state) => const TourismNightLife(),
        ),
        GoRoute(
          path: '/tourism/transport',
          builder: (context, state) => const TourismTransport(),
        ),
        GoRoute(
          path: '/tourism/travel-agencies',
          builder: (context, state) => const TourismTravelAgencies(),
        ),
        GoRoute(
          path: '/confirm-email/:email',
          builder: (context, state) {
            final email = state.pathParameters['email'] ?? '';
            return EmailConfirmationPage(email: email);
          },
        ),

        // ✅ Added Deep Linking for Verify Email
        GoRoute(
            path: '/Users/<USER>',
            builder: (context, state) {
              final userId = state.uri.queryParameters['userId'] ?? '';
              final token =
                  state.uri.queryParameters['token']?.replaceAll(' ', '+') ??
                      '';
              return EmailVerificationScreen(userId: userId, token: token);
            }),
        GoRoute(
          path: '/events',
          builder: (context, state) => const EventsScreen(),
        ),
        GoRoute(
          path: '/events/event-details/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return EventsDetailsScreen(id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general',
          builder: (context, state) => const GeneralInformationScreen(),
        ),
        GoRoute(
          path: '/general/establishments',
          builder: (context, state) => const EstablishmentsScreen(),
        ),
        GoRoute(
          path: '/general/establishments/restaurants',
          builder: (context, state) => const Restaurants(),
        ),
        GoRoute(
          path: '/general/establishments/coffes',
          builder: (context, state) => const Coffees(),
        ),
        GoRoute(
          path: '/general/establishments/bars',
          builder: (context, state) => const Bars(),
        ),
        GoRoute(
          path: '/general/establishments/pastryShops',
          builder: (context, state) => const PastryShops(),
        ),
        GoRoute(
          path: '/general/gas-stations',
          builder: (context, state) => const GasStations(),
        ),
        GoRoute(
          path: '/general/shops',
          builder: (context, state) => const Shops(),
        ),
        GoRoute(
          path: '/general/accommodations',
          builder: (context, state) => const AccommodationsScreen(),
        ),
        GoRoute(
          path: '/general/accommodations/hotels',
          builder: (context, state) => const Hotels(),
        ),
        GoRoute(
          path: '/general/accommodations/guestHouses',
          builder: (context, state) => const GuestHouse(),
        ),
        GoRoute(
          path: '/general/finance',
          builder: (context, state) => const FinanceScreen(),
        ),
        GoRoute(
          path: '/general/finance/banks',
          builder: (context, state) => const Banks(),
        ),
        GoRoute(
          path: '/general/finance/currencyExchanges',
          builder: (context, state) => const CurrencyExchanges(),
        ),
        GoRoute(
          path: '/general/finance/insuranceCompanies',
          builder: (context, state) => const InsuranceCompanies(),
        ),
        GoRoute(
          path: '/general/finance/atms',
          builder: (context, state) => const ATMs(),
        ),
        GoRoute(
          path: '/general/ecology',
          builder: (context, state) => const EcologyScreen(),
        ),
        GoRoute(
          path: '/general/ecology/bioShops',
          builder: (context, state) => const BioShops(),
        ),
        GoRoute(
          path: '/general/ecology/farms',
          builder: (context, state) => const Farms(),
        ),
        GoRoute(
          path: '/general/ecology/recyclings',
          builder: (context, state) => const Recycling(),
        ),
        GoRoute(
          path: '/general/ecology/ecoInitiatives',
          builder: (context, state) => const EcoInitiatives(),
        ),
        GoRoute(
          path: '/general/culture',
          builder: (context, state) => const CultureScreen(),
        ),
        GoRoute(
          path: '/general/culture/museums',
          builder: (context, state) => const Museums(),
        ),
        GoRoute(
          path: '/general/culture/theaters',
          builder: (context, state) => const Theaters(),
        ),
        GoRoute(
          path: '/general/culture/galleries',
          builder: (context, state) => const Galleries(),
        ),
        GoRoute(
          path: '/general/education',
          builder: (context, state) => const EducationScreen(),
        ),
        GoRoute(
          path: '/general/education/kindergardens',
          builder: (context, state) => const Kindergardens(),
        ),
        GoRoute(
          path: '/general/education/nurseries',
          builder: (context, state) => const Nurseries(),
        ),
        GoRoute(
          path: '/general/education/childNutritionCenters',
          builder: (context, state) => const ChildNutritionCenter(),
        ),
        GoRoute(
          path: '/general/education/schools',
          builder: (context, state) => const Schools(),
        ),
        GoRoute(
          path: '/general/education/universities',
          builder: (context, state) => const Universities(),
        ),
        GoRoute(
          path: '/general/education/developmentCenters',
          builder: (context, state) => const DevelopmentCenters(),
        ),
        GoRoute(
          path: '/general/health',
          builder: (context, state) => const HealthScreen(),
        ),
        GoRoute(
          path: '/general/health/pharmacies',
          builder: (context, state) => const Pharmacies(),
        ),
        GoRoute(
          path: '/general/health/medicalEstablishments',
          builder: (context, state) => const MedicalEstablishments(),
        ),
        GoRoute(
          path: '/general/health/doctorsOffices',
          builder: (context, state) => const DoctorsOffices(),
        ),
        GoRoute(
          path: '/general/health/medicalLabs',
          builder: (context, state) => const MedicalLabs(),
        ),
        GoRoute(
          path: '/general/health/veterinaries',
          builder: (context, state) => const Veterinaries(),
        ),
        GoRoute(
          path: '/general/sport',
          builder: (context, state) => const SportScreen(),
        ),
        GoRoute(
          path: '/general/sport/sportFacilities',
          builder: (context, state) => const SportFacilities(),
        ),
        GoRoute(
          path: '/news',
          builder: (context, state) => const NewsScreen(),
        ),
        GoRoute(
          path: '/taxes',
          builder: (context, state) => const MunicipalityTax(),
        ),
        // Detail Routes for Shops
        GoRoute(
          path: '/general/shop/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'shop', id: int.parse(id));
          },
        ),
        // Detail Routes for General Establishments
        GoRoute(
          path: '/general/establishments/restaurant/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'restaurant', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/establishments/coffee/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'coffe', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/establishments/bar/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'bar', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/establishments/pastryShop/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'pastryShop', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/gasStation/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'gasStation', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/accommodations/hotel/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'hotel', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/accommodations/guestHouse/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'guestHouse', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/finance/bank/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'bank', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/finance/currencyExchange/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'currencyExchange', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/finance/insuranceCompany/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'insuranceCompany', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/finance/atm/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'atm', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/ecology/bioShop/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'bioShop', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/ecology/farm/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'farm', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/ecology/recycling/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'recycling', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/ecology/ecoInitiative/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'ecoInitiative', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/culture/museum/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'museum', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/culture/theater/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'theater', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/culture/gallery/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'gallery', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/education/kindergarden/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'kindergarden', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/education/nursery/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'nursery', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/education/childNutritionCenter/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'childNutritionCenter', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/education/school/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'school', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/education/university/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'university', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/education/developmentCenter/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'developmentCenter', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/health/pharmacy/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'pharmacy', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/health/medicalEstablishment/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'medicalEstablishment', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/health/doctorsOffice/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'doctorsOffice', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/health/medicalLab/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'medicalLab', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/health/veterinary/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'veterinary', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/general/sport/sportFacility/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'sportFacility', id: int.parse(id));
          },
        ),
        // Detail Routes for Tourism Categories
        GoRoute(
          path: '/tourism/legendsAndMyths/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'legendsAdnMyths', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/tourism/landmarks/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'landmarks', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/tourism/culture/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'culture', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/tourism/hiking/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'hiking', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/tourism/family/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'family', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/tourism/nightLife/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'nightLife', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/tourism/transport/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'transport', id: int.parse(id));
          },
        ),
        GoRoute(
          path: '/tourism/toruistAgency/:id',
          builder: (context, state) {
            final id = state.pathParameters['id']!;
            return InterestPointDetailsScreen(
                category: 'toruistAgency', id: int.parse(id));
          },
        ),
      ],
    ),
    GoRoute(
        path: '/registration',
        builder: (context, state) => const RegisterScreen()),
    GoRoute(path: '/login', builder: (context, state) => const LoginScreen()),
    GoRoute(path: '/weather', builder: (context, state) => WeatherScreen()),
  ],
);
