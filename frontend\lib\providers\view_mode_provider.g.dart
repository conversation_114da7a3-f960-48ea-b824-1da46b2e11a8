// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'view_mode_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$viewModeStateHash() => r'5004c0cb6e9317dd4dde690e8877efab39293023';

/// See also [ViewModeState].
@ProviderFor(ViewModeState)
final viewModeStateProvider =
    AutoDisposeNotifierProvider<ViewModeState, ViewMode>.internal(
  ViewModeState.new,
  name: r'viewModeStateProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$viewModeStateHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$ViewModeState = AutoDisposeNotifier<ViewMode>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
