import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'l10n_bg.dart';
import 'l10n_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of S
/// returned by `S.of(context)`.
///
/// Applications need to include `S.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'generated/l10n.dart';
///
/// return MaterialApp(
///   localizationsDelegates: S.localizationsDelegates,
///   supportedLocales: S.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the S.supportedLocales
/// property.
abstract class S {
  S(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static S of(BuildContext context) {
    return Localizations.of<S>(context, S)!;
  }

  static const LocalizationsDelegate<S> delegate = _SDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('bg'),
    Locale('en')
  ];

  /// No description provided for @appTitle.
  ///
  /// In en, this message translates to:
  /// **'Smart Yambol'**
  String get appTitle;

  /// No description provided for @guest.
  ///
  /// In en, this message translates to:
  /// **'Guest'**
  String get guest;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @darkMode.
  ///
  /// In en, this message translates to:
  /// **'Dark Mode'**
  String get darkMode;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @terms.
  ///
  /// In en, this message translates to:
  /// **'Terms & Conditions'**
  String get terms;

  /// No description provided for @login.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// No description provided for @tourism.
  ///
  /// In en, this message translates to:
  /// **'Tourism'**
  String get tourism;

  /// No description provided for @tourismDescription.
  ///
  /// In en, this message translates to:
  /// **'Explore the beauty of Yambol and create lifelong memories!'**
  String get tourismDescription;

  /// No description provided for @weather.
  ///
  /// In en, this message translates to:
  /// **'Weather'**
  String get weather;

  /// No description provided for @weatherDescription.
  ///
  /// In en, this message translates to:
  /// **'Stay prepared! See the forecast and air quality.'**
  String get weatherDescription;

  /// No description provided for @events.
  ///
  /// In en, this message translates to:
  /// **'Events'**
  String get events;

  /// No description provided for @eventsDescription.
  ///
  /// In en, this message translates to:
  /// **'Check out cultural and sports events in the city.'**
  String get eventsDescription;

  /// No description provided for @repairs.
  ///
  /// In en, this message translates to:
  /// **'Repairs'**
  String get repairs;

  /// No description provided for @repairsDescription.
  ///
  /// In en, this message translates to:
  /// **'Stay informed! See where and when repairs are happening.'**
  String get repairsDescription;

  /// No description provided for @parking.
  ///
  /// In en, this message translates to:
  /// **'Parking'**
  String get parking;

  /// No description provided for @parkingDescription.
  ///
  /// In en, this message translates to:
  /// **'Find a parking spot or pay for blue zone parking via SMS.'**
  String get parkingDescription;

  /// No description provided for @generalInfo.
  ///
  /// In en, this message translates to:
  /// **'General Info'**
  String get generalInfo;

  /// No description provided for @generalInfoDescription.
  ///
  /// In en, this message translates to:
  /// **'Discover everything you need to know about Yambol in one place.'**
  String get generalInfoDescription;

  /// No description provided for @transport.
  ///
  /// In en, this message translates to:
  /// **'Transport'**
  String get transport;

  /// No description provided for @transportDescription.
  ///
  /// In en, this message translates to:
  /// **'Up-to-date information on public transport routes and schedules.'**
  String get transportDescription;

  /// No description provided for @cameras.
  ///
  /// In en, this message translates to:
  /// **'Cameras'**
  String get cameras;

  /// No description provided for @camerasDescription.
  ///
  /// In en, this message translates to:
  /// **'Live-stream views from key locations in the city.'**
  String get camerasDescription;

  /// No description provided for @news.
  ///
  /// In en, this message translates to:
  /// **'News'**
  String get news;

  /// No description provided for @newsDescription.
  ///
  /// In en, this message translates to:
  /// **'Stay updated! Follow the latest news and events.'**
  String get newsDescription;

  /// No description provided for @taxes.
  ///
  /// In en, this message translates to:
  /// **'Taxes'**
  String get taxes;

  /// No description provided for @taxesDescription.
  ///
  /// In en, this message translates to:
  /// **'View and pay your municipal taxes quickly and easily.'**
  String get taxesDescription;

  /// No description provided for @publicServices.
  ///
  /// In en, this message translates to:
  /// **'Services'**
  String get publicServices;

  /// No description provided for @publicServicesDescription.
  ///
  /// In en, this message translates to:
  /// **'Take advantage of the services we offer!'**
  String get publicServicesDescription;

  /// No description provided for @reports.
  ///
  /// In en, this message translates to:
  /// **'Reports'**
  String get reports;

  /// No description provided for @reportsDescription.
  ///
  /// In en, this message translates to:
  /// **'Report an issue in the city and help make it better.'**
  String get reportsDescription;

  /// No description provided for @favorites.
  ///
  /// In en, this message translates to:
  /// **'Favorites'**
  String get favorites;

  /// No description provided for @location.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @messages.
  ///
  /// In en, this message translates to:
  /// **'Messages'**
  String get messages;

  /// No description provided for @emailOrUsername.
  ///
  /// In en, this message translates to:
  /// **'Email or Username'**
  String get emailOrUsername;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @forgotPassword.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password?'**
  String get forgotPassword;

  /// No description provided for @loginButton.
  ///
  /// In en, this message translates to:
  /// **'Log In'**
  String get loginButton;

  /// No description provided for @orContinueWith.
  ///
  /// In en, this message translates to:
  /// **'- or continue with -'**
  String get orContinueWith;

  /// No description provided for @createNewAccount.
  ///
  /// In en, this message translates to:
  /// **'Create a new account'**
  String get createNewAccount;

  /// No description provided for @registerHere.
  ///
  /// In en, this message translates to:
  /// **'here'**
  String get registerHere;

  /// No description provided for @register.
  ///
  /// In en, this message translates to:
  /// **'Register'**
  String get register;

  /// No description provided for @confirmPassword.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPassword;

  /// No description provided for @agreeToTerms.
  ///
  /// In en, this message translates to:
  /// **'By clicking the Register button, you agree to the'**
  String get agreeToTerms;

  /// No description provided for @registerTerms.
  ///
  /// In en, this message translates to:
  /// **'terms & conditions'**
  String get registerTerms;

  /// No description provided for @registerButton.
  ///
  /// In en, this message translates to:
  /// **'Register'**
  String get registerButton;

  /// No description provided for @alreadyHaveAccount.
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get alreadyHaveAccount;

  /// No description provided for @loginHere.
  ///
  /// In en, this message translates to:
  /// **'Log in here'**
  String get loginHere;

  /// No description provided for @dontHaveAccount.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get dontHaveAccount;

  /// No description provided for @firstName.
  ///
  /// In en, this message translates to:
  /// **'First Name'**
  String get firstName;

  /// No description provided for @lastName.
  ///
  /// In en, this message translates to:
  /// **'Last Name'**
  String get lastName;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @emailRequired.
  ///
  /// In en, this message translates to:
  /// **'Email is required'**
  String get emailRequired;

  /// No description provided for @invalidEmail.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email'**
  String get invalidEmail;

  /// No description provided for @registrationSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Registration successful!'**
  String get registrationSuccessful;

  /// No description provided for @passwordRequired.
  ///
  /// In en, this message translates to:
  /// **'Password is required'**
  String get passwordRequired;

  /// No description provided for @passwordTooShort.
  ///
  /// In en, this message translates to:
  /// **'Password is too short'**
  String get passwordTooShort;

  /// No description provided for @confirmPasswordRequired.
  ///
  /// In en, this message translates to:
  /// **'Confirm password required'**
  String get confirmPasswordRequired;

  /// No description provided for @passwordsDoNotMatch.
  ///
  /// In en, this message translates to:
  /// **'Passwords do not match'**
  String get passwordsDoNotMatch;

  /// No description provided for @firstNameRequired.
  ///
  /// In en, this message translates to:
  /// **'First name is required'**
  String get firstNameRequired;

  /// No description provided for @lastNameRequired.
  ///
  /// In en, this message translates to:
  /// **'Last name is required'**
  String get lastNameRequired;

  /// No description provided for @tourismWelcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Explore Yambol! Discover its landmarks and get inspired by its history.'**
  String get tourismWelcomeMessage;

  /// No description provided for @tourismLegendsAndMyths.
  ///
  /// In en, this message translates to:
  /// **'Legends and Myths'**
  String get tourismLegendsAndMyths;

  /// No description provided for @tourismSights.
  ///
  /// In en, this message translates to:
  /// **'Landmarks'**
  String get tourismSights;

  /// No description provided for @tourismCulturalAndArtisticSites.
  ///
  /// In en, this message translates to:
  /// **'Cultural and Artistic Sites'**
  String get tourismCulturalAndArtisticSites;

  /// No description provided for @tourismRoutesAndActivities.
  ///
  /// In en, this message translates to:
  /// **'Routes and Activities'**
  String get tourismRoutesAndActivities;

  /// No description provided for @tourismFamilyEntertainment.
  ///
  /// In en, this message translates to:
  /// **'Family Entertainment'**
  String get tourismFamilyEntertainment;

  /// No description provided for @tourismNightlife.
  ///
  /// In en, this message translates to:
  /// **'Nightlife'**
  String get tourismNightlife;

  /// No description provided for @tourismTransport.
  ///
  /// In en, this message translates to:
  /// **'Transport'**
  String get tourismTransport;

  /// No description provided for @tourismTravelAgencies.
  ///
  /// In en, this message translates to:
  /// **'Travel Agencies'**
  String get tourismTravelAgencies;

  /// No description provided for @loginSuccess.
  ///
  /// In en, this message translates to:
  /// **'Login successful'**
  String get loginSuccess;

  /// No description provided for @loginError.
  ///
  /// In en, this message translates to:
  /// **'Login failed'**
  String get loginError;

  /// No description provided for @legendsAndMythsWelcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Discover the mysteries of Yambol: Legends and myths that will captivate you'**
  String get legendsAndMythsWelcomeMessage;

  /// No description provided for @weatherTitle.
  ///
  /// In en, this message translates to:
  /// **'Weather'**
  String get weatherTitle;

  /// No description provided for @cityName.
  ///
  /// In en, this message translates to:
  /// **'Yambol'**
  String get cityName;

  /// No description provided for @wind.
  ///
  /// In en, this message translates to:
  /// **'Wind'**
  String get wind;

  /// No description provided for @humidity.
  ///
  /// In en, this message translates to:
  /// **'Humidity'**
  String get humidity;

  /// No description provided for @errorLoadingWeather.
  ///
  /// In en, this message translates to:
  /// **'Error loading weather data'**
  String get errorLoadingWeather;

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// No description provided for @monday.
  ///
  /// In en, this message translates to:
  /// **'Monday'**
  String get monday;

  /// No description provided for @tuesday.
  ///
  /// In en, this message translates to:
  /// **'Tuesday'**
  String get tuesday;

  /// No description provided for @wednesday.
  ///
  /// In en, this message translates to:
  /// **'Wednesday'**
  String get wednesday;

  /// No description provided for @thursday.
  ///
  /// In en, this message translates to:
  /// **'Thursday'**
  String get thursday;

  /// No description provided for @friday.
  ///
  /// In en, this message translates to:
  /// **'Friday'**
  String get friday;

  /// No description provided for @saturday.
  ///
  /// In en, this message translates to:
  /// **'Saturday'**
  String get saturday;

  /// No description provided for @sunday.
  ///
  /// In en, this message translates to:
  /// **'Sunday'**
  String get sunday;

  /// No description provided for @eventsWelcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Don\'t miss anything! Explore cultural and sports events in the city!'**
  String get eventsWelcomeMessage;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search...'**
  String get search;

  /// No description provided for @tourismCulturalSites.
  ///
  /// In en, this message translates to:
  /// **'Cultural sites'**
  String get tourismCulturalSites;

  /// No description provided for @map.
  ///
  /// In en, this message translates to:
  /// **'Map'**
  String get map;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @sportEvents.
  ///
  /// In en, this message translates to:
  /// **'Sport'**
  String get sportEvents;

  /// No description provided for @cultureEvents.
  ///
  /// In en, this message translates to:
  /// **'Culture'**
  String get cultureEvents;

  /// No description provided for @celebrationEvents.
  ///
  /// In en, this message translates to:
  /// **'Celebrations'**
  String get celebrationEvents;

  /// No description provided for @mon.
  ///
  /// In en, this message translates to:
  /// **'Mon'**
  String get mon;

  /// No description provided for @tue.
  ///
  /// In en, this message translates to:
  /// **'Tue'**
  String get tue;

  /// No description provided for @wed.
  ///
  /// In en, this message translates to:
  /// **'Wed'**
  String get wed;

  /// No description provided for @thu.
  ///
  /// In en, this message translates to:
  /// **'Thu'**
  String get thu;

  /// No description provided for @fri.
  ///
  /// In en, this message translates to:
  /// **'Fri'**
  String get fri;

  /// No description provided for @sat.
  ///
  /// In en, this message translates to:
  /// **'Sat'**
  String get sat;

  /// No description provided for @sun.
  ///
  /// In en, this message translates to:
  /// **'Sun'**
  String get sun;

  /// No description provided for @january.
  ///
  /// In en, this message translates to:
  /// **'January'**
  String get january;

  /// No description provided for @february.
  ///
  /// In en, this message translates to:
  /// **'February'**
  String get february;

  /// No description provided for @march.
  ///
  /// In en, this message translates to:
  /// **'March'**
  String get march;

  /// No description provided for @april.
  ///
  /// In en, this message translates to:
  /// **'April'**
  String get april;

  /// No description provided for @may.
  ///
  /// In en, this message translates to:
  /// **'May'**
  String get may;

  /// No description provided for @june.
  ///
  /// In en, this message translates to:
  /// **'June'**
  String get june;

  /// No description provided for @july.
  ///
  /// In en, this message translates to:
  /// **'July'**
  String get july;

  /// No description provided for @august.
  ///
  /// In en, this message translates to:
  /// **'August'**
  String get august;

  /// No description provided for @september.
  ///
  /// In en, this message translates to:
  /// **'September'**
  String get september;

  /// No description provided for @october.
  ///
  /// In en, this message translates to:
  /// **'October'**
  String get october;

  /// No description provided for @november.
  ///
  /// In en, this message translates to:
  /// **'November'**
  String get november;

  /// No description provided for @december.
  ///
  /// In en, this message translates to:
  /// **'December'**
  String get december;

  /// No description provided for @emailConfirmationTitle.
  ///
  /// In en, this message translates to:
  /// **'Email Confirmation'**
  String get emailConfirmationTitle;

  /// No description provided for @emailConfirmationMessage.
  ///
  /// In en, this message translates to:
  /// **'Please check your email to confirm your account.'**
  String get emailConfirmationMessage;

  /// No description provided for @didNotReceiveEmail.
  ///
  /// In en, this message translates to:
  /// **'Didn\'t receive the email?'**
  String get didNotReceiveEmail;

  /// No description provided for @resendVerificationEmail.
  ///
  /// In en, this message translates to:
  /// **'Resend Verification Email'**
  String get resendVerificationEmail;

  /// No description provided for @verificationEmailResent.
  ///
  /// In en, this message translates to:
  /// **'Verification email resent!'**
  String get verificationEmailResent;

  /// No description provided for @errorOccurred.
  ///
  /// In en, this message translates to:
  /// **'Error occurred'**
  String get errorOccurred;

  /// No description provided for @emailVerificationTitle.
  ///
  /// In en, this message translates to:
  /// **'Email Verification'**
  String get emailVerificationTitle;

  /// No description provided for @verifyEmailButton.
  ///
  /// In en, this message translates to:
  /// **'Verify Email'**
  String get verifyEmailButton;

  /// No description provided for @emailVerifiedSuccessfully.
  ///
  /// In en, this message translates to:
  /// **'Email verified successfully!'**
  String get emailVerifiedSuccessfully;

  /// No description provided for @generalInfoWelcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Yambol – business, education, and essential city information!'**
  String get generalInfoWelcomeMessage;

  /// No description provided for @gasStations.
  ///
  /// In en, this message translates to:
  /// **'Gas stations'**
  String get gasStations;

  /// No description provided for @generalInformationFull.
  ///
  /// In en, this message translates to:
  /// **'General information'**
  String get generalInformationFull;

  /// No description provided for @shop.
  ///
  /// In en, this message translates to:
  /// **'Shops'**
  String get shop;

  /// No description provided for @restaurants.
  ///
  /// In en, this message translates to:
  /// **'Restaurants'**
  String get restaurants;

  /// No description provided for @coffee.
  ///
  /// In en, this message translates to:
  /// **'Coffee'**
  String get coffee;

  /// No description provided for @bars.
  ///
  /// In en, this message translates to:
  /// **'Bars'**
  String get bars;

  /// No description provided for @pastryShops.
  ///
  /// In en, this message translates to:
  /// **'Pastry shops'**
  String get pastryShops;

  /// No description provided for @establishments.
  ///
  /// In en, this message translates to:
  /// **'Establishments'**
  String get establishments;

  /// No description provided for @establishmentsWelcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Cozy restaurants, traditional cuisine, and modern venues with a unique atmosphere!'**
  String get establishmentsWelcomeMessage;

  /// No description provided for @hotels.
  ///
  /// In en, this message translates to:
  /// **'Hotels'**
  String get hotels;

  /// No description provided for @guestHouses.
  ///
  /// In en, this message translates to:
  /// **'Guest houses'**
  String get guestHouses;

  /// No description provided for @accommodation.
  ///
  /// In en, this message translates to:
  /// **'Accommodation'**
  String get accommodation;

  /// No description provided for @accommodationWelcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Comfortable hotels, cozy guesthouses, and great places to stay for every taste!'**
  String get accommodationWelcomeMessage;

  /// No description provided for @finance.
  ///
  /// In en, this message translates to:
  /// **'Finance'**
  String get finance;

  /// No description provided for @banks.
  ///
  /// In en, this message translates to:
  /// **'Banks'**
  String get banks;

  /// No description provided for @currencyExchanges.
  ///
  /// In en, this message translates to:
  /// **'Currency Exchange'**
  String get currencyExchanges;

  /// No description provided for @insuranceCompanies.
  ///
  /// In en, this message translates to:
  /// **'Insurance'**
  String get insuranceCompanies;

  /// No description provided for @atms.
  ///
  /// In en, this message translates to:
  /// **'ATMs'**
  String get atms;

  /// No description provided for @financeYambolWelcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Secure banking services, affordable loans, and financial solutions for you in Yambol!'**
  String get financeYambolWelcomeMessage;

  /// No description provided for @bioShops.
  ///
  /// In en, this message translates to:
  /// **'Bio shops'**
  String get bioShops;

  /// No description provided for @farms.
  ///
  /// In en, this message translates to:
  /// **'Farms'**
  String get farms;

  /// No description provided for @recycling.
  ///
  /// In en, this message translates to:
  /// **'Recycling'**
  String get recycling;

  /// No description provided for @ecoInitiatives.
  ///
  /// In en, this message translates to:
  /// **'Eco initiatives'**
  String get ecoInitiatives;

  /// No description provided for @ecology.
  ///
  /// In en, this message translates to:
  /// **'Ecology'**
  String get ecology;

  /// No description provided for @ecoYambolWelcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Clean nature, sustainable future, and eco-friendly solutions for Yambol!'**
  String get ecoYambolWelcomeMessage;

  /// No description provided for @culture.
  ///
  /// In en, this message translates to:
  /// **'Culture'**
  String get culture;

  /// No description provided for @museums.
  ///
  /// In en, this message translates to:
  /// **'Museums'**
  String get museums;

  /// No description provided for @theaters.
  ///
  /// In en, this message translates to:
  /// **'Theaters'**
  String get theaters;

  /// No description provided for @galleries.
  ///
  /// In en, this message translates to:
  /// **'Galleries'**
  String get galleries;

  /// No description provided for @cultureYambolWelcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Rich cultural heritage, inspiring art, and traditions in Yambol!'**
  String get cultureYambolWelcomeMessage;

  /// No description provided for @education.
  ///
  /// In en, this message translates to:
  /// **'Education'**
  String get education;

  /// No description provided for @kindergardens.
  ///
  /// In en, this message translates to:
  /// **'Kindergardens'**
  String get kindergardens;

  /// No description provided for @nursery.
  ///
  /// In en, this message translates to:
  /// **'Nursery'**
  String get nursery;

  /// No description provided for @childNutritionCenter.
  ///
  /// In en, this message translates to:
  /// **'Child nutrition center'**
  String get childNutritionCenter;

  /// No description provided for @schools.
  ///
  /// In en, this message translates to:
  /// **'Schools'**
  String get schools;

  /// No description provided for @universities.
  ///
  /// In en, this message translates to:
  /// **'Universities'**
  String get universities;

  /// No description provided for @developmentCenters.
  ///
  /// In en, this message translates to:
  /// **'Development centers'**
  String get developmentCenters;

  /// No description provided for @health.
  ///
  /// In en, this message translates to:
  /// **'Health'**
  String get health;

  /// No description provided for @pharmacies.
  ///
  /// In en, this message translates to:
  /// **'Pharmacies'**
  String get pharmacies;

  /// No description provided for @medicalEstablishments.
  ///
  /// In en, this message translates to:
  /// **'Medical establishments'**
  String get medicalEstablishments;

  /// No description provided for @doctorsOffices.
  ///
  /// In en, this message translates to:
  /// **'Doctors offices'**
  String get doctorsOffices;

  /// No description provided for @medicalLabs.
  ///
  /// In en, this message translates to:
  /// **'Medical labs'**
  String get medicalLabs;

  /// No description provided for @veterinaries.
  ///
  /// In en, this message translates to:
  /// **'Veterinaries'**
  String get veterinaries;

  /// No description provided for @healthYambolWelcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Health and care for everyone – quality medical services and a healthy lifestyle in Yambol!'**
  String get healthYambolWelcomeMessage;

  /// No description provided for @educationYambolWelcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Quality education for a bright future – innovative learning and development in Yambol!'**
  String get educationYambolWelcomeMessage;

  /// No description provided for @sport.
  ///
  /// In en, this message translates to:
  /// **'Sport'**
  String get sport;

  /// No description provided for @sportFacilities.
  ///
  /// In en, this message translates to:
  /// **'Sport Facilities'**
  String get sportFacilities;

  /// No description provided for @sportYambolWelcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Sports and an active lifestyle – modern facilities and opportunities for movement in Yambol!'**
  String get sportYambolWelcomeMessage;

  /// No description provided for @newsYambolWelcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Stay informed! Follow the latest news and events.'**
  String get newsYambolWelcomeMessage;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @description.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// No description provided for @municipalityTaxes.
  ///
  /// In en, this message translates to:
  /// **'Municipality Taxes'**
  String get municipalityTaxes;

  /// No description provided for @information.
  ///
  /// In en, this message translates to:
  /// **'Information'**
  String get information;

  /// No description provided for @chooseWayToPay.
  ///
  /// In en, this message translates to:
  /// **'You can pay online using your CIN or with an electronic signature (which must be installed on your device).'**
  String get chooseWayToPay;

  /// No description provided for @waysToPay.
  ///
  /// In en, this message translates to:
  /// **'Ways to pay'**
  String get waysToPay;

  /// No description provided for @kinNumber.
  ///
  /// In en, this message translates to:
  /// **'Pay with KIN'**
  String get kinNumber;

  /// No description provided for @kinDesc.
  ///
  /// In en, this message translates to:
  /// **'Fast and convenient payment with your KIN number'**
  String get kinDesc;

  /// No description provided for @electronicSignature.
  ///
  /// In en, this message translates to:
  /// **'Pay with electronic signature'**
  String get electronicSignature;

  /// No description provided for @electronicSignatureDesc.
  ///
  /// In en, this message translates to:
  /// **'Secure payment with your electronic signature'**
  String get electronicSignatureDesc;

  /// No description provided for @userHelp.
  ///
  /// In en, this message translates to:
  /// **'Need help? Contact us at phone: 0875 333 844'**
  String get userHelp;

  /// No description provided for @problemElectronicSignature.
  ///
  /// In en, this message translates to:
  /// **'The electronic signature payment link could not be opened.'**
  String get problemElectronicSignature;

  /// No description provided for @problemKin.
  ///
  /// In en, this message translates to:
  /// **'The KIN payment link could not be opened.'**
  String get problemKin;

  /// No description provided for @navigate.
  ///
  /// In en, this message translates to:
  /// **'Navigate'**
  String get navigate;

  /// No description provided for @business.
  ///
  /// In en, this message translates to:
  /// **'Business'**
  String get business;

  /// No description provided for @landmarks.
  ///
  /// In en, this message translates to:
  /// **'Landmarks'**
  String get landmarks;

  /// No description provided for @healthcare.
  ///
  /// In en, this message translates to:
  /// **'Healthcare'**
  String get healthcare;

  /// No description provided for @sports.
  ///
  /// In en, this message translates to:
  /// **'Sports'**
  String get sports;

  /// No description provided for @applyFilters.
  ///
  /// In en, this message translates to:
  /// **'Apply Filters'**
  String get applyFilters;

  /// No description provided for @filterCategories.
  ///
  /// In en, this message translates to:
  /// **'Filter Categories'**
  String get filterCategories;
}

class _SDelegate extends LocalizationsDelegate<S> {
  const _SDelegate();

  @override
  Future<S> load(Locale locale) {
    return SynchronousFuture<S>(lookupS(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['bg', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_SDelegate old) => false;
}

S lookupS(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'bg': return SBg();
    case 'en': return SEn();
  }

  throw FlutterError(
    'S.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
