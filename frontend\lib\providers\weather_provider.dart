import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/infrastructure/weather/weather_service.dart';
import 'package:frontend/models/weather_command.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'weather_provider.g.dart';

final weatherServiceProvider =
    Provider<WeatherService>((ref) => WeatherService());

@riverpod
Future<Weather> weather(Ref ref) async {
  final weatherService = ref.read(weatherServiceProvider);
  return await weatherService.fetchWeather();
}
