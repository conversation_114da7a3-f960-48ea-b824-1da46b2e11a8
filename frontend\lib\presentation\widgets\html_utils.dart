import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:html/dom.dart' as dom;

class HtmlUtils {
  /// Sanitizes HTML content by removing inline background-color and color styles
  static String sanitizeHtml(String rawHtml) {
    // Remove background-color inline styles (any value)
    String withoutBgColor = rawHtml.replaceAll(
      RegExp(r'background-color\s*:\s*[^;"]+;?', caseSensitive: false),
      '',
    );

    // Remove color inline styles (any value)
    String withoutColor = withoutBgColor.replaceAll(
      RegExp(r'color\s*:\s*[^;"]+;?', caseSensitive: false),
      '',
    );

    return withoutColor;
  }

  /// Returns styled HTML styles map based on theme mode
  static Map<String, Style> getHtmlStyles({
    required bool isDarkMode,
    double baseFontSize = 14.0,
    double lineHeight = 1.5,
    String fontFamily = 'Roboto',
  }) {
    return {
      // Base body styles
      "body": Style(
        fontFamily: fontFamily,
        fontWeight: FontWeight.w400,
        fontSize: FontSize(baseFontSize),
        lineHeight: LineHeight(lineHeight),
        color: isDarkMode ? const Color(0xFFE0E0E0) : const Color(0xFF606060),
        margin: Margins.all(0),
        padding: HtmlPaddings.all(0),
      ),

      // Paragraph styles
      "p": Style(
        margin: Margins.only(bottom: 8),
        fontFamily: fontFamily,
        fontWeight: FontWeight.w400,
        fontSize: FontSize(baseFontSize),
        lineHeight: LineHeight(lineHeight),
        color: isDarkMode ? const Color(0xFFE0E0E0) : const Color(0xFF606060),
      ),

      // Heading styles
      "h1": Style(
        fontSize: FontSize(baseFontSize + 4),
        fontWeight: FontWeight.bold,
        margin: Margins.only(top: 12, bottom: 8),
        color: isDarkMode ? Colors.white : const Color(0xFF424242),
      ),
      "h2": Style(
        fontSize: FontSize(baseFontSize + 2),
        fontWeight: FontWeight.bold,
        margin: Margins.only(top: 10, bottom: 6),
        color: isDarkMode ? Colors.white : const Color(0xFF424242),
      ),
      "h3": Style(
        fontSize: FontSize(baseFontSize + 1),
        fontWeight: FontWeight.bold,
        margin: Margins.only(top: 8, bottom: 4),
        color: isDarkMode ? Colors.white : const Color(0xFF424242),
      ),

      // List styles
      "ul": Style(
        margin: Margins.only(top: 4, bottom: 8, left: 12),
      ),
      "ol": Style(
        margin: Margins.only(top: 4, bottom: 8, left: 12),
      ),
      "li": Style(
        margin: Margins.only(bottom: 2),
        fontSize: FontSize(baseFontSize),
        lineHeight: LineHeight(lineHeight),
        color: isDarkMode ? const Color(0xFFE0E0E0) : const Color(0xFF606060),
      ),

      // Link styles
      "a": Style(
        color: isDarkMode ? const Color(0xFF64B5F6) : const Color(0xFF1976D2),
        textDecoration: TextDecoration.underline,
      ),

      // Strong/Bold text
      "strong": Style(
        fontWeight: FontWeight.bold,
      ),
      "b": Style(
        fontWeight: FontWeight.bold,
      ),

      // Italic text
      "em": Style(
        fontStyle: FontStyle.italic,
      ),
      "i": Style(
        fontStyle: FontStyle.italic,
      ),

      // Line breaks
      "br": Style(
        margin: Margins.only(bottom: 4),
      ),
    };
  }

  /// Creates a styled HTML widget with consistent theming
  static Widget createStyledHtmlWidget({
    required String htmlContent,
    required bool isDarkMode,
    double baseFontSize = 14.0,
    double lineHeight = 1.5,
    String fontFamily = 'Roboto',
    Function(String?, Map<String, String>, dom.Element?)? onLinkTap,
  }) {
    return Html(
      data: sanitizeHtml(htmlContent),
      style: getHtmlStyles(
        isDarkMode: isDarkMode,
        baseFontSize: baseFontSize,
        lineHeight: lineHeight,
        fontFamily: fontFamily,
      ),
      onLinkTap: onLinkTap ??
          (url, attributes, element) {
            print('Link tapped: $url');
          },
    );
  }
}
