import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'filter_provider.g.dart';

enum FilterType { all, sportEvents, cultureEvents, celebrationEvents }

@riverpod
class FilterState extends _$FilterState {
  @override
  FilterType build() => FilterType.all; // Default to 'All' filter

  void setFilter(FilterType filterType) => state = filterType;

  void setAllFilter() => state = FilterType.all;

  void setSportEventsFilter() => state = FilterType.sportEvents;

  void setCultureEventsFilter() => state = FilterType.cultureEvents;

  void setCelebrationEventsFilter() => state = FilterType.celebrationEvents;

  bool isSelected(FilterType filterType) => state == filterType;

  String getFilterName() {
    switch (state) {
      case FilterType.all:
        return 'All';
      case FilterType.sportEvents:
        return 'Sport Events';
      case FilterType.cultureEvents:
        return 'Culture Events';
      case FilterType.celebrationEvents:
        return 'Celebration Events';
      default:
        return 'All';
    }
  }
}
