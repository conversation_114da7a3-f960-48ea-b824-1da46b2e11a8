// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$selectedIndexHash() => r'54b904fc7ffa7904f7039477833bb1a41dba1a61';

/// See also [SelectedIndex].
@ProviderFor(SelectedIndex)
final selectedIndexProvider =
    AutoDisposeNotifierProvider<SelectedIndex, int>.internal(
  SelectedIndex.new,
  name: r'selectedIndexProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$selectedIndexHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SelectedIndex = AutoDisposeNotifier<int>;
String _$layoutModeHash() => r'15315671ad7196f915dbbfbad3e0565545ed9f59';

/// See also [LayoutMode].
@ProviderFor(LayoutMode)
final layoutModeProvider =
    AutoDisposeNotifierProvider<LayoutMode, bool>.internal(
  LayoutMode.new,
  name: r'layoutModeProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$layoutModeHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$LayoutMode = AutoDisposeNotifier<bool>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
