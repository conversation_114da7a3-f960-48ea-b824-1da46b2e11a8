import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../l10n/generated/l10n.dart';
import '../../models/register_command.dart';
import '../../providers/auth_providers.dart';
import 'dart:typed_data';

class RegisterActions extends ConsumerWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController emailController;
  final TextEditingController passwordController;
  final TextEditingController confirmPasswordController;
  final TextEditingController firstNameController;
  final TextEditingController lastNameController;
  final Uint8List? profilePicture;

  const RegisterActions({
    Key? key,
    required this.formKey,
    required this.emailController,
    required this.passwordController,
    required this.confirmPasswordController,
    required this.firstNameController,
    required this.lastNameController,
    this.profilePicture,
  }) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final registerState = ref.watch(authNotifierProvider);

    // Listen to registration state changes
    ref.listen<AsyncValue<void>>(authNotifierProvider, (previous, state) {
      if (state is AsyncData) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(S.of(context).registrationSuccessful),
            backgroundColor: Colors.green,
          ),
        );

        final email = emailController.text.trim();
        if (email.isNotEmpty && previous is! AsyncData) {
          context.go(
              '/confirm-email/$email'); // Ensure navigation happens only once
        }
      } else if (state is AsyncError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              (state.error as dynamic)?.message ?? S.of(context).register,
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    });

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Text.rich(
            TextSpan(
              text: S.of(context).agreeToTerms,
              children: [
                TextSpan(
                  text: " ${S.of(context).registerTerms}",
                  style: const TextStyle(
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 10),
        if (registerState is AsyncError)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text(
              (registerState.error as dynamic)?.message ??
                  S.of(context).register,
              style: const TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: registerState is AsyncLoading
                ? null
                : () async {
                    if (formKey.currentState!.validate()) {
                      final registerCommand = RegisterCommand(
                        email: emailController.text.trim(),
                        password: passwordController.text.trim(),
                        confirmPassword: confirmPasswordController.text.trim(),
                        firstName: firstNameController.text.trim(),
                        lastName: lastNameController.text.trim(),
                        profilePicture: profilePicture,
                      );

                      await ref
                          .read(authNotifierProvider.notifier)
                          .registerUser(registerCommand);
                    }
                  },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: registerState is AsyncLoading
                ? const CircularProgressIndicator(color: Colors.white)
                : Text(
                    S.of(context).registerButton,
                    style: const TextStyle(fontSize: 16, color: Colors.white),
                  ),
          ),
        ),
        const SizedBox(height: 20),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildSocialButton("assets/icons/google.png"),
            const SizedBox(width: 16),
            _buildSocialButton("assets/icons/facebook.png"),
          ],
        ),
        const SizedBox(height: 20),
        // Fixed: Wrap the Row in Flexible/Expanded or use Wrap widget
        Wrap(
          alignment: WrapAlignment.center,
          crossAxisAlignment: WrapCrossAlignment.center,
          children: [
            Text(S.of(context).alreadyHaveAccount),
            TextButton(
              onPressed: () {
                context.pushReplacement('/login');
              },
              child: Text(
                S.of(context).loginHere,
                style: const TextStyle(color: Colors.green),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSocialButton(String assetPath) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: Colors.black12),
      ),
      child: Image.asset(
        assetPath,
        height: 30,
        width: 30,
      ),
    );
  }
}
