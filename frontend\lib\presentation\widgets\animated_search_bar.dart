import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:frontend/l10n/generated/l10n.dart';

class AnimatedSearchTextFeild extends StatefulWidget {
  // Add onSubmitted callback parameter
  final Function(String)? onSubmitted;

  const AnimatedSearchTextFeild({
    super.key,
    this.onSubmitted, // Make it optional
  });

  @override
  State<AnimatedSearchTextFeild> createState() =>
      _AnimatedSearchTextFeildState();
}

class _AnimatedSearchTextFeildState extends State<AnimatedSearchTextFeild> {
  bool opened = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 500),
      width: opened ? MediaQuery.of(context).size.width * .72 : 56,
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(32),
        color: Colors.white,
        boxShadow: kElevationToShadow[2],
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              child: Padding(
                padding: const EdgeInsets.only(left: 16),
                child: opened
                    ? TextField(
                        controller: _searchController,
                        onSubmitted: (value) {
                          // Call the parent's onSubmitted callback if provided
                          if (widget.onSubmitted != null) {
                            widget.onSubmitted!(value);
                          }

                          setState(() {
                            opened = !opened;
                          });
                        },
                        style: const TextStyle(
                          color: Colors.black,
                        ),
                        decoration: InputDecoration(
                          hintText: S.of(context).search,
                          hintStyle: TextStyle(
                            color: Colors.black,
                          ),
                          border: InputBorder.none,
                        ),
                      )
                    : null,
              ),
            ),
          ),
          Material(
            type: MaterialType.transparency,
            child: opened
                ? InkWell(
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(0),
                        topRight: Radius.circular(32),
                        bottomLeft: Radius.circular(0),
                        bottomRight: Radius.circular(32)),
                    onTap: () {
                      // Clear search text when closing
                      _searchController.clear();
                      setState(() {
                        opened = !opened;
                      });
                    },
                    child: const Padding(
                      padding: EdgeInsets.all(18.0),
                      child: Icon(
                        Icons.close,
                        color: Colors.black,
                        size: 20,
                      ),
                    ),
                  )
                : InkWell(
                    borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(32),
                        topRight: Radius.circular(32),
                        bottomLeft: Radius.circular(32),
                        bottomRight: Radius.circular(32)),
                    onTap: () {
                      setState(() {
                        opened = !opened;
                      });
                    },
                    child: const Padding(
                      padding: EdgeInsets.all(18.0),
                      child: Icon(
                        Icons.search,
                        color: Colors.black,
                        size: 20,
                      ),
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}
