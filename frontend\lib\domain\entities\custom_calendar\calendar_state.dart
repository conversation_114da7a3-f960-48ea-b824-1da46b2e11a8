/// State class for the custom calendar
class CalendarState {
  final DateTime displayedMonth;
  final DateTime? selectedDate;

  const CalendarState({
    required this.displayedMonth,
    this.selectedDate,
  });

  CalendarState copyWith({
    DateTime? displayedMonth,
    DateTime? selectedDate,
    bool clearSelectedDate = false,
  }) {
    return CalendarState(
      displayedMonth: displayedMonth ?? this.displayedMonth,
      selectedDate:
          clearSelectedDate ? null : (selectedDate ?? this.selectedDate),
    );
  }
}
