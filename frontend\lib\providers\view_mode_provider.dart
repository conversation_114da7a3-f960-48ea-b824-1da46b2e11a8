import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'view_mode_provider.g.dart';

enum ViewMode { calendar, list }

@riverpod
class ViewModeState extends _$ViewModeState {
  @override
  ViewMode build() => ViewMode.list; // Default to calendar view

  void setCalendarView() => state = ViewMode.calendar;

  void setListView() => state = ViewMode.list;

  void toggleView() {
    state = state == ViewMode.calendar ? ViewMode.list : ViewMode.calendar;
  }

  bool get isCalendarView => state == ViewMode.calendar;

  bool get isListView => state == ViewMode.list;
}
