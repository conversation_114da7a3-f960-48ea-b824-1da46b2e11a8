<svg width="28" height="36" viewBox="0 0 28 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_740_3717)">
<ellipse cx="12" cy="29" rx="4" ry="2" fill="black" fill-opacity="0.12"/>
</g>
<mask id="path-2-outside-1_740_3717" maskUnits="userSpaceOnUse" x="1.33301" y="1.25" width="26" height="32" fill="black">
<rect fill="white" x="1.33301" y="1.25" width="26" height="32"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.9016 22.5711C23.791 20.5234 25.6663 17.2229 25.6663 13.5C25.6663 7.2868 20.443 2.25 13.9997 2.25C7.55635 2.25 2.33301 7.2868 2.33301 13.5C2.33301 17.2229 4.20832 20.5234 7.09774 22.5711C9.28849 24.1629 12.3745 26.672 13.0951 30.7071C13.1745 31.1518 13.5479 31.4966 13.9997 31.4966C14.4514 31.4966 14.8249 31.1518 14.9043 30.7071C15.6248 26.6719 18.7109 24.1629 20.9016 22.5711Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.9016 22.5711C23.791 20.5234 25.6663 17.2229 25.6663 13.5C25.6663 7.2868 20.443 2.25 13.9997 2.25C7.55635 2.25 2.33301 7.2868 2.33301 13.5C2.33301 17.2229 4.20832 20.5234 7.09774 22.5711C9.28849 24.1629 12.3745 26.672 13.0951 30.7071C13.1745 31.1518 13.5479 31.4966 13.9997 31.4966C14.4514 31.4966 14.8249 31.1518 14.9043 30.7071C15.6248 26.6719 18.7109 24.1629 20.9016 22.5711Z" fill="#FFC0CB"/>
<path d="M20.9016 22.5711L20.3234 21.7551L20.3138 21.7621L20.9016 22.5711ZM7.09774 22.5711L7.6856 21.762L7.67595 21.7552L7.09774 22.5711ZM13.0951 30.7071L12.1106 30.8829L12.1106 30.8829L13.0951 30.7071ZM14.9043 30.7071L15.8887 30.8829L15.8887 30.8829L14.9043 30.7071ZM21.4799 23.3869C24.6146 21.1654 26.6663 17.57 26.6663 13.5H24.6663C24.6663 16.8757 22.9675 19.8814 20.3234 21.7552L21.4799 23.3869ZM26.6663 13.5C26.6663 6.70055 20.9607 1.25 13.9997 1.25V3.25C19.9253 3.25 24.6663 7.87305 24.6663 13.5H26.6663ZM13.9997 1.25C7.03866 1.25 1.33301 6.70055 1.33301 13.5H3.33301C3.33301 7.87305 8.07405 3.25 13.9997 3.25V1.25ZM1.33301 13.5C1.33301 17.57 3.38476 21.1654 6.51953 23.387L7.67595 21.7552C5.03189 19.8814 3.33301 16.8758 3.33301 13.5H1.33301ZM14.0795 30.5313C13.2833 26.0726 9.88416 23.3596 7.68556 21.7621L6.50992 23.3801C8.69282 24.9662 11.4657 27.2714 12.1106 30.8829L14.0795 30.5313ZM13.9997 30.4966C14.0152 30.4966 14.0312 30.4997 14.0451 30.5052C14.0584 30.5105 14.0669 30.5166 14.0715 30.5207C14.076 30.5246 14.0778 30.5275 14.0784 30.5287C14.0792 30.53 14.0794 30.5309 14.0795 30.5313L12.1106 30.8829C12.2663 31.7544 13.0143 32.4966 13.9997 32.4966V30.4966ZM13.9199 30.5313C13.9199 30.5309 13.9202 30.53 13.9209 30.5287C13.9216 30.5275 13.9234 30.5246 13.9278 30.5207C13.9325 30.5166 13.9409 30.5105 13.9542 30.5052C13.9682 30.4997 13.9842 30.4966 13.9997 30.4966V32.4966C14.9851 32.4966 15.7331 31.7544 15.8887 30.8829L13.9199 30.5313ZM20.3138 21.7621C18.1152 23.3596 14.716 26.0726 13.9199 30.5313L15.8887 30.8829C16.5336 27.2713 19.3066 24.9662 21.4895 23.38L20.3138 21.7621Z" fill="url(#paint0_linear_740_3717)" mask="url(#path-2-outside-1_740_3717)"/>
<g clip-path="url(#clip0_740_3717)">
<path d="M6.33124 17.6625C7.63437 18.7906 9.70624 20.2406 11.5219 19.9656C11.7125 19.9375 11.9 19.8875 12.0844 19.825C11.7969 19.4406 11.5437 19.0625 11.3281 18.7187C10.6437 17.625 10.4281 16.3687 10.5187 15.1687C9.87499 15.2969 9.29374 15.575 8.80937 15.9625C8.60624 16.125 8.29999 16.0031 8.34687 15.7437C8.54687 14.6969 9.37812 13.8406 10.4781 13.6719C10.5594 13.6594 10.6437 13.65 10.725 13.6469L11.3312 9.54375C11.3937 9.1125 11.5812 8.52187 12.1125 8.10937C12.6937 7.6625 13.7031 7.15625 15.35 7.00625L15.275 6.94062C14.6437 6.45312 13.0125 5.64062 9.49062 6.175C5.96874 6.70937 4.64062 7.96875 4.17812 8.62187C3.99999 8.87187 3.97499 9.1875 4.01874 9.49375L4.77499 14.6281C4.94687 15.7937 5.44687 16.8969 6.33124 17.6656V17.6625ZM9.07187 10.8C9.20937 10.7031 9.40937 10.7375 9.44062 10.9031C9.44374 10.9187 9.44687 10.9375 9.44999 10.9531C9.54999 11.6344 9.08749 12.2656 8.41562 12.3687C7.74374 12.4719 7.11874 12 7.01874 11.3219C7.01562 11.3062 7.01562 11.2875 7.01249 11.2719C6.99374 11.1031 7.17499 11.0094 7.33437 11.0625C7.61562 11.1562 7.92187 11.1844 8.23124 11.1375C8.54062 11.0906 8.82812 10.9719 9.06874 10.8H9.07187ZM12.175 18.1875C13.0937 19.6531 14.6594 21.6531 16.475 21.9281C18.2906 22.2031 20.3656 20.7562 21.6656 19.625C22.55 18.8594 23.05 17.7562 23.2219 16.5875L23.9781 11.4531C24.0219 11.1469 23.9969 10.8312 23.8187 10.5812C23.3562 9.92812 22.0281 8.66875 18.5062 8.13437C14.9844 7.6 13.3562 8.4125 12.725 8.9C12.4812 9.0875 12.3656 9.38125 12.3219 9.6875L11.5656 14.8219C11.3937 15.9875 11.5531 17.1906 12.1781 18.1875H12.175ZM16.6406 13.3531C16.4 13.1812 16.1156 13.0625 15.8031 13.0156C15.4906 12.9687 15.1844 12.9969 14.9062 13.0906C14.7469 13.1437 14.5656 13.05 14.5844 12.8812C14.5875 12.8656 14.5875 12.8469 14.5906 12.8312C14.6906 12.15 15.3156 11.6812 15.9875 11.7844C16.6594 11.8875 17.1219 12.5187 17.0219 13.2C17.0187 13.2156 17.0156 13.2344 17.0125 13.25C16.9812 13.4156 16.7812 13.45 16.6437 13.3531H16.6406ZM20.8969 13.8375C20.8656 14.0031 20.6656 14.0375 20.5281 13.9406C20.2875 13.7687 20.0031 13.65 19.6906 13.6031C19.3781 13.5562 19.0719 13.5844 18.7937 13.6781C18.6344 13.7312 18.4531 13.6375 18.4719 13.4687C18.475 13.4531 18.475 13.4344 18.4781 13.4187C18.5781 12.7375 19.2031 12.2687 19.875 12.3719C20.5469 12.475 21.0094 13.1062 20.9094 13.7875C20.9062 13.8031 20.9031 13.8219 20.9 13.8375H20.8969ZM20.5625 16.9437C19.95 18.3406 18.475 19.2094 16.9125 18.9719C15.35 18.7344 14.1906 17.4656 14.0062 15.95C13.975 15.6906 14.2844 15.5719 14.4812 15.7406C15.2281 16.3906 16.1562 16.8437 17.2 17C18.2437 17.1562 19.2656 17.0031 20.1656 16.6C20.4031 16.4937 20.6656 16.7 20.5594 16.9406L20.5625 16.9437Z" fill="white"/>
</g>
<defs>
<filter id="filter0_f_740_3717" x="6" y="25" width="12" height="8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_740_3717"/>
</filter>
<linearGradient id="paint0_linear_740_3717" x1="13.9997" y1="2.25" x2="13.9997" y2="31.4966" gradientUnits="userSpaceOnUse">
<stop stop-color="#626262"/>
<stop offset="1" stop-color="#626262"/>
</linearGradient>
<clipPath id="clip0_740_3717">
<rect width="20" height="16" fill="white" transform="translate(4 6)"/>
</clipPath>
</defs>
</svg>
