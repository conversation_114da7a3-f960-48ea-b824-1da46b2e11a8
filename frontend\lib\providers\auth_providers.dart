import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../infrastructure/authentifiaction/authentification_service.dart';
import '../models/register_command.dart';
import '../models/login_command.dart';

part 'auth_providers.g.dart';

@riverpod
class AuthNotifier extends _$AuthNotifier {
  @override
  FutureOr<void> build() {} // Required override

  /// **Register User API Call**
  Future<void> registerUser(RegisterCommand command) async {
    state = const AsyncLoading(); // Show loading state
    try {
      final response = await AuthenticationService.register(command);

      if (response != null && response.statusCode == 200) {
        state = const AsyncData(null); // Success
      } else {
        throw Exception(
          "Registration failed with status code ${response?.statusCode}",
        );
      }
    } catch (e) {
      state = AsyncError(e, StackTrace.current); // Handle API errors
    }
  }

  /// **Login User API Call**
  Future<void> loginUser(LoginCommand command) async {
    state = const AsyncLoading(); // Show loading state
    try {
      final response = await AuthenticationService.login(command);

      if (response != null && response.statusCode == 200) {
        state = const AsyncData(null); // Success
      } else {
        throw Exception(
          "Login failed with status code ${response?.statusCode}",
        );
      }
    } catch (e) {
      state = AsyncError(e, StackTrace.current); // Handle API errors
    }
  }

  /// **Resend Verification Email API Call**
  Future<void> resendVerificationEmail(String email) async {
    state = const AsyncLoading(); // Show loading state
    try {
      final response = await AuthenticationService.resendVerification(email);

      if (response != null && response.statusCode == 204) {
        state = const AsyncData(null); // Success
      } else {
        throw Exception(
          "Resend verification failed with status code ${response?.statusCode}",
        );
      }
    } catch (e) {
      state = AsyncError(e, StackTrace.current); // Handle API errors
    }
  }

  /// **Verify Email API Call**
  Future<void> verifyEmail(String userId, String confirmationToken) async {
    state = const AsyncLoading(); // Show loading state
    try {
      final response =
          await AuthenticationService.verifyEmail(userId, confirmationToken);

      if (response != null && response.statusCode == 204) {
        state = const AsyncData(null); // ✅ Success
      } else {
        throw Exception(
          "Email verification failed with status code ${response?.statusCode}",
        );
      }
    } catch (e) {
      state = AsyncError(e, StackTrace.current); // Handle API errors
    }
  }
}
