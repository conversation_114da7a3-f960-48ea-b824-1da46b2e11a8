import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/infrastructure/points-of-interest/interest_points_details_service.dart';
import 'package:frontend/models/get_interest_points_by_id_command.dart';

import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'interest_points_details.provider.g.dart';

@riverpod
Future<InterestPointDetailsModel> fetchInterestPointDetails(
    Ref ref, int id, String languageCode) async {
  // Fetch the interest point details based on the language code and id
  return InterestPointDetailsService.getInterestPointDetails(id, languageCode);
}
