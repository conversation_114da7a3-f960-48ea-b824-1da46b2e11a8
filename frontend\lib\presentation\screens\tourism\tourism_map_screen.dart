import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:frontend/presentation/widgets/reusable_map_screen.dart';

class TourismMapScreen extends ConsumerWidget {
  const TourismMapScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // These are the same categories from your original implementation
    const List<String> tourismCategories = [
      'Landmark',
      'LegendsAndMyths',
      'Culture',
      'Hiking',
      'Family',
      'NightLife',
      'Transport',
      'TouristAgency'
    ];

    return Scaffold(
      body: ReusableMapWidget(
        fixedCategories: tourismCategories,
        showFilters: false, // No filters for tourism screen
        title: 'Tourism Map', // Optional, for debugging
      ),
    );
  }
}
