class EventsGetModel {
  final int id;
  final String name;
  final String? description; // Nullable string for description
  final String category;
  final DateTime? startDate; // Nullable DateTime for startDate

  EventsGetModel({
    required this.id,
    required this.name,
    this.description,
    required this.category,
    this.startDate,
  });

  // Factory constructor to create an EventsGetModel from a JSON map
  factory EventsGetModel.fromJson(Map<String, dynamic> json) {
    return EventsGetModel(
      id: json['id'],
      name: json['name'],
      description: json['description'], // Can be null
      category: json['category'],
      startDate: json['startDate'] != null
          ? DateTime.parse(json['startDate'])
          : null, // Parse ISO string to DateTime if not null
    );
  }

  // Method to convert an EventsGetModel to a JSON map
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description, // Optional field
      'category': category,
      'startDate': startDate
          ?.toIso8601String(), // Convert DateTime to ISO string if not null
    };
  }

  @override
  String toString() {
    return 'EventsGetModel(id: $id, name: $name, description: $description, category: $category, startDate: $startDate)';
  }
}
