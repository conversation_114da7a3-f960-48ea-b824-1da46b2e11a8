{"inputs": ["C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\.dart_tool\\flutter_build\\9a7833175e0f293248f04934c85f6e3c\\app.dill", "C:\\Users\\<USER>\\fvm\\versions\\3.27.2\\packages\\flutter_tools\\lib\\src\\build_system\\targets\\icon_tree_shaker.dart", "C:\\Users\\<USER>\\fvm\\versions\\3.27.2\\bin\\internal\\engine.version", "C:\\Users\\<USER>\\fvm\\versions\\3.27.2\\bin\\internal\\engine.version", "C:\\Users\\<USER>\\fvm\\versions\\3.27.2\\bin\\internal\\engine.version", "C:\\Users\\<USER>\\fvm\\versions\\3.27.2\\bin\\internal\\engine.version", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\pubspec.yaml", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\images\\general-info.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\images\\landmark-marker.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\images\\login_background.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\images\\news-image.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\images\\register_background.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\images\\settings_image.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\images\\tourism.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\atm-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\bank-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\bar-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\bio-shop-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\child-nutrition-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\coffe-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\culture-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\currency-change-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\development-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\doctors-office-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\eco-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\facebook.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\family-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\farm-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\gallery-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\gas-station-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\google.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\guest-house-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\hiking-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\hotel-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\insurance-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\kindergarden-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\landmark-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\legends-and-myths-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\medical-establishment-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\medical-lab-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\museum-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\night-life-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\nursery-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\pastry-shop-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\pharmacy-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\recycle-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\restaurants-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\school-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\shop-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\sport-facility-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\theaters-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\transport-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\travel-agency-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\university-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\icons\\veterinary-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\.env.staging", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\.env.local", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\map_styles\\dark_map.json", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\assets\\map_styles\\light_map.json", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\assets\\CupertinoIcons.ttf", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\weather_icons-3.0.0\\lib\\fonts\\weathericons-regular-webfont.ttf", "C:\\Users\\<USER>\\fvm\\versions\\3.27.2\\bin\\cache\\artifacts\\material_fonts\\MaterialIcons-Regular.otf", "C:\\Users\\<USER>\\fvm\\versions\\3.27.2\\packages\\flutter\\lib\\src\\material\\shaders\\ink_sparkle.frag", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\_fe_analyzer_shared-76.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\analyzer-6.11.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\analyzer_plugin-0.11.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\app_links-6.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\app_links_linux-1.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\app_links_platform_interface-2.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\app_links_web-1.0.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\args-2.6.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\async-2.11.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\boolean_selector-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build-2.4.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_config-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_daemon-4.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_resolvers-2.4.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_runner-2.4.14\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\build_runner_core-8.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\built_collection-5.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\built_value-8.9.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\carousel_slider-5.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\checked_yaml-2.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\clock-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\code_builder-4.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.19.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\convert-3.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cross_file-0.3.4+2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\csslib-1.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\cupertino_icons-1.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\curved_navigation_bar-1.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\custom_lint_core-0.7.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\custom_lint_visitor-1.0.0+6.11.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dart_style-2.3.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_frame-1.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\device_preview-1.2.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dio-5.8.0+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\dio_web_adapter-2.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fake_async-1.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\file-7.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\fixnum-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_dotenv-5.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_html-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_image_compress-2.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_image_compress_common-1.0.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_image_compress_macos-1.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_image_compress_ohos-0.0.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_image_compress_platform_interface-1.0.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_image_compress_web-0.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_lints-5.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_plugin_android_lifecycle-2.0.24\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_riverpod-2.6.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_secure_storage-9.2.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_secure_storage_linux-1.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_secure_storage_macos-3.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_secure_storage_platform_interface-1.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_secure_storage_web-1.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_secure_storage_windows-3.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_svg-2.0.17\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\freezed_annotation-2.4.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\frontend_server_client-4.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator-10.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_android-4.6.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_apple-2.3.13\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_platform_interface-4.2.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_web-2.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\geolocator_windows-0.2.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\glob-2.1.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\go_router-14.8.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps-8.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter-2.11.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_android-2.15.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_ios-2.14.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_platform_interface-2.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_maps_flutter_web-0.5.12\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\graphs-2.3.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\gtk-2.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\html-0.15.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_multi_server-3.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl-0.19.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\intl_translation-0.20.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\io-1.0.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\js-0.6.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\json_annotation-4.9.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker-10.0.7\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_flutter_testing-3.0.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\leak_tracker_testing-3.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\lints-5.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\list_counter-1.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\logging-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\macros-0.1.3-main.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\matcher-0.12.16+1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.11.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.15.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\mime-2.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\nested-1.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\package_config-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_parsing-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.15\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\persistent_bottom_nav_bar-6.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\petitparser-6.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pool-1.5.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\provider-6.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pub_semver-2.1.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\pubspec_parse-1.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod-2.6.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod_analyzer_utils-0.5.8\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod_annotation-2.6.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\riverpod_generator-2.6.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sanitize_html-2.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences-2.5.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.5\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_foundation-2.5.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_linux-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_platform_interface-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_web-2.4.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_windows-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shelf-1.4.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shelf_web_socket-2.0.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shimmer-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_gen-1.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\sprintf-7.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stack_trace-1.12.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\state_notifier-1.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_channel-2.1.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\stream_transform-2.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\test_api-0.7.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\timing-1.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.4.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.16\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_ios-6.3.3\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_web-2.4.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\uuid-4.5.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_graphics-1.1.18\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_graphics_codec-1.1.13\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_graphics_compiler-1.1.16\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vm_service-14.3.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\watcher-1.1.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\weather_icons-3.0.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket-0.1.6\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\web_socket_channel-3.0.2\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\win32-5.10.1\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xml-6.5.0\\LICENSE", "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\yaml-3.1.3\\LICENSE", "C:\\Users\\<USER>\\fvm\\versions\\3.27.2\\bin\\cache\\dart-sdk\\pkg\\_macros\\LICENSE", "C:\\Users\\<USER>\\fvm\\versions\\3.27.2\\bin\\cache\\pkg\\sky_engine\\LICENSE", "C:\\Users\\<USER>\\fvm\\versions\\3.27.2\\packages\\flutter\\LICENSE", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\DOES_NOT_EXIST_RERUN_FOR_WILDCARD740620830"], "outputs": ["C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/general-info.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/landmark-marker.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/login_background.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/news-image.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/register_background.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/settings_image.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/images/tourism.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/atm-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/bank-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/bar-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/bio-shop-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/child-nutrition-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/coffe-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/culture-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/currency-change-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/development-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/doctors-office-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/eco-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/facebook.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/family-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/farm-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/gallery-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/gas-station-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/google.png", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/guest-house-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/hiking-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/hotel-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/insurance-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/kindergarden-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/landmark-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/legends-and-myths-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/medical-establishment-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/medical-lab-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/museum-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/night-life-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/nursery-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/pastry-shop-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/pharmacy-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/recycle-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/restaurants-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/school-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/shop-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/sport-facility-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/theaters-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/transport-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/travel-agency-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/university-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/icons/veterinary-icon.svg", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\.env.staging", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\.env.local", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/map_styles/dark_map.json", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\assets/map_styles/light_map.json", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/weather_icons/lib/fonts/weathericons-regular-webfont.ttf", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "C:\\Users\\<USER>\\Documents\\Smart-Yambol\\frontend\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z"]}