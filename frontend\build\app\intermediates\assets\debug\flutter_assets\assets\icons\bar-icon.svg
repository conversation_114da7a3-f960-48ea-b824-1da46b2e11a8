<svg width="28" height="36" viewBox="0 0 28 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_740_3717)">
<ellipse cx="12" cy="29" rx="4" ry="2" fill="black" fill-opacity="0.12"/>
</g>
<mask id="path-2-outside-1_740_3717" maskUnits="userSpaceOnUse" x="1.33301" y="1.25" width="26" height="32" fill="black">
<rect fill="white" x="1.33301" y="1.25" width="26" height="32"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.9016 22.5711C23.791 20.5234 25.6663 17.2229 25.6663 13.5C25.6663 7.2868 20.443 2.25 13.9997 2.25C7.55635 2.25 2.33301 7.2868 2.33301 13.5C2.33301 17.2229 4.20832 20.5234 7.09774 22.5711C9.28849 24.1629 12.3745 26.672 13.0951 30.7071C13.1745 31.1518 13.5479 31.4966 13.9997 31.4966C14.4514 31.4966 14.8249 31.1518 14.9043 30.7071C15.6248 26.6719 18.7109 24.1629 20.9016 22.5711Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.9016 22.5711C23.791 20.5234 25.6663 17.2229 25.6663 13.5C25.6663 7.2868 20.443 2.25 13.9997 2.25C7.55635 2.25 2.33301 7.2868 2.33301 13.5C2.33301 17.2229 4.20832 20.5234 7.09774 22.5711C9.28849 24.1629 12.3745 26.672 13.0951 30.7071C13.1745 31.1518 13.5479 31.4966 13.9997 31.4966C14.4514 31.4966 14.8249 31.1518 14.9043 30.7071C15.6248 26.6719 18.7109 24.1629 20.9016 22.5711Z" fill="#008000"/>
<path d="M20.9016 22.5711L20.3234 21.7551L20.3138 21.7621L20.9016 22.5711ZM7.09774 22.5711L7.6856 21.762L7.67595 21.7552L7.09774 22.5711ZM13.0951 30.7071L12.1106 30.8829L12.1106 30.8829L13.0951 30.7071ZM14.9043 30.7071L15.8887 30.8829L15.8887 30.8829L14.9043 30.7071ZM21.4799 23.3869C24.6146 21.1654 26.6663 17.57 26.6663 13.5H24.6663C24.6663 16.8757 22.9675 19.8814 20.3234 21.7552L21.4799 23.3869ZM26.6663 13.5C26.6663 6.70055 20.9607 1.25 13.9997 1.25V3.25C19.9253 3.25 24.6663 7.87305 24.6663 13.5H26.6663ZM13.9997 1.25C7.03866 1.25 1.33301 6.70055 1.33301 13.5H3.33301C3.33301 7.87305 8.07405 3.25 13.9997 3.25V1.25ZM1.33301 13.5C1.33301 17.57 3.38476 21.1654 6.51953 23.387L7.67595 21.7552C5.03189 19.8814 3.33301 16.8758 3.33301 13.5H1.33301ZM14.0795 30.5313C13.2833 26.0726 9.88416 23.3596 7.68556 21.7621L6.50992 23.3801C8.69282 24.9662 11.4657 27.2714 12.1106 30.8829L14.0795 30.5313ZM13.9997 30.4966C14.0152 30.4966 14.0312 30.4997 14.0451 30.5052C14.0584 30.5105 14.0669 30.5166 14.0715 30.5207C14.076 30.5246 14.0778 30.5275 14.0784 30.5287C14.0792 30.53 14.0794 30.5309 14.0795 30.5313L12.1106 30.8829C12.2663 31.7544 13.0143 32.4966 13.9997 32.4966V30.4966ZM13.9199 30.5313C13.9199 30.5309 13.9202 30.53 13.9209 30.5287C13.9216 30.5275 13.9234 30.5246 13.9278 30.5207C13.9325 30.5166 13.9409 30.5105 13.9542 30.5052C13.9682 30.4997 13.9842 30.4966 13.9997 30.4966V32.4966C14.9851 32.4966 15.7331 31.7544 15.8887 30.8829L13.9199 30.5313ZM20.3138 21.7621C18.1152 23.3596 14.716 26.0726 13.9199 30.5313L15.8887 30.8829C16.5336 27.2713 19.3066 24.9662 21.4895 23.38L20.3138 21.7621Z" fill="url(#paint0_linear_740_3717)" mask="url(#path-2-outside-1_740_3717)"/>
<path d="M8.66667 19.3136V13.7062C7.8963 13.5094 7.25926 13.1159 6.75556 12.5257C6.25185 11.9354 6 11.2468 6 10.4598C6 9.71498 6.22607 9.05446 6.67822 8.47826C7.13037 7.90206 7.7117 7.50154 8.42222 7.27668C8.76296 6.60211 9.27052 6.05402 9.94489 5.63241C10.6193 5.2108 11.3784 5 12.2222 5C12.7407 5 13.2261 5.08432 13.6782 5.25296C14.1304 5.42161 14.5413 5.64646 14.9111 5.92754C15.0593 5.89943 15.2 5.87469 15.3333 5.85333C15.4667 5.83197 15.6148 5.82157 15.7778 5.82213C16.7556 5.82213 17.5926 6.15239 18.2889 6.81291C18.9852 7.47343 19.3333 8.26746 19.3333 9.19499C19.3333 9.50417 19.2927 9.7993 19.2116 10.0804C19.1304 10.3614 19.008 10.6285 18.8444 10.8814H20.2222C20.7111 10.8814 21.1298 11.0467 21.4782 11.3772C21.8267 11.7078 22.0006 12.1046 22 12.5679V17.6271C22 18.0909 21.8261 18.4881 21.4782 18.8186C21.1304 19.1491 20.7117 19.3141 20.2222 19.3136H18.4444C18.1333 19.6087 17.8779 19.9637 17.6782 20.3785C17.4785 20.7934 17.1413 21.0006 16.6667 21H10.4444C9.95556 21 9.53718 20.835 9.18933 20.505C8.84148 20.1751 8.66726 19.7779 8.66667 19.3136ZM7.77778 10.4598C7.77778 10.9236 7.952 11.3207 8.30044 11.6513C8.64889 11.9818 9.06726 12.1468 9.55556 12.1462C10.0296 12.1462 10.4335 11.9987 10.7671 11.7036C11.1007 11.4084 11.445 11.0782 11.8 10.7128C12.1704 10.3333 12.589 9.98537 13.056 9.66888C13.523 9.35239 14.1339 9.19443 14.8889 9.19499H17.5556C17.5556 8.73122 17.3816 8.33435 17.0338 8.00437C16.6859 7.6744 16.2673 7.50913 15.7778 7.50856C15.4074 7.50856 15.0963 7.55438 14.8444 7.64601L14.4667 7.78261L13.7778 7.23452C13.6148 7.10804 13.4036 6.98493 13.144 6.86519C12.8844 6.74545 12.5772 6.68587 12.2222 6.68643C11.7481 6.68643 11.3147 6.80588 10.9218 7.0448C10.5289 7.28371 10.229 7.60694 10.0222 8.01449L9.71111 8.6469L9 8.87879C8.62963 8.99122 8.33333 9.19162 8.11111 9.48C7.88889 9.76838 7.77778 10.095 7.77778 10.4598ZM18.4444 17.6271H20.2222V12.5679H18.4444V17.6271Z" fill="white"/>
<defs>
<filter id="filter0_f_740_3717" x="6" y="25" width="12" height="8" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_740_3717"/>
</filter>
<linearGradient id="paint0_linear_740_3717" x1="13.9997" y1="2.25" x2="13.9997" y2="31.4966" gradientUnits="userSpaceOnUse">
<stop stop-color="#626262"/>
<stop offset="1" stop-color="#626262"/>
</linearGradient>
</defs>
</svg>
